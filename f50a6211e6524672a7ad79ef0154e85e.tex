% Options for packages loaded elsewhere
\PassOptionsToPackage{unicode}{hyperref}
\PassOptionsToPackage{hyphens}{url}
%
\documentclass[
]{article}
\usepackage{amsmath,amssymb}
\usepackage{lmodern}
\usepackage{iftex}
\ifPDFTeX
  \usepackage[T1]{fontenc}
  \usepackage[utf8]{inputenc}
  \usepackage{textcomp} % provide euro and other symbols
\else % if luatex or xetex
  \usepackage{unicode-math}
  \defaultfontfeatures{Scale=MatchLowercase}
  \defaultfontfeatures[\rmfamily]{Ligatures=TeX,Scale=1}
\fi
% Use upquote if available, for straight quotes in verbatim environments
\IfFileExists{upquote.sty}{\usepackage{upquote}}{}
\IfFileExists{microtype.sty}{% use microtype if available
  \usepackage[]{microtype}
  \UseMicrotypeSet[protrusion]{basicmath} % disable protrusion for tt fonts
}{}
\makeatletter
\@ifundefined{KOMAClassName}{% if non-KOMA class
  \IfFileExists{parskip.sty}{%
    \usepackage{parskip}
  }{% else
    \setlength{\parindent}{0pt}
    \setlength{\parskip}{6pt plus 2pt minus 1pt}}
}{% if KOMA class
  \KOMAoptions{parskip=half}}
\makeatother
\usepackage{xcolor}
\usepackage{longtable,booktabs,array}
\usepackage{multirow}
\usepackage{calc} % for calculating minipage widths
% Correct order of tables after \paragraph or \subparagraph
\usepackage{etoolbox}
\makeatletter
\patchcmd\longtable{\par}{\if@noskipsec\mbox{}\fi\par}{}{}
\makeatother
% Allow footnotes in longtable head/foot
\IfFileExists{footnotehyper.sty}{\usepackage{footnotehyper}}{\usepackage{footnote}}
\makesavenoteenv{longtable}
\usepackage{graphicx}
\makeatletter
\def\maxwidth{\ifdim\Gin@nat@width>\linewidth\linewidth\else\Gin@nat@width\fi}
\def\maxheight{\ifdim\Gin@nat@height>\textheight\textheight\else\Gin@nat@height\fi}
\makeatother
% Scale images if necessary, so that they will not overflow the page
% margins by default, and it is still possible to overwrite the defaults
% using explicit options in \includegraphics[width, height, ...]{}
\setkeys{Gin}{width=\maxwidth,height=\maxheight,keepaspectratio}
% Set default figure placement to htbp
\makeatletter
\def\fps@figure{htbp}
\makeatother
\setlength{\emergencystretch}{3em} % prevent overfull lines
\providecommand{\tightlist}{%
  \setlength{\itemsep}{0pt}\setlength{\parskip}{0pt}}
\setcounter{secnumdepth}{-\maxdimen} % remove section numbering
\ifLuaTeX
  \usepackage{selnolig}  % disable illegal ligatures
\fi
\IfFileExists{bookmark.sty}{\usepackage{bookmark}}{\usepackage{hyperref}}
\IfFileExists{xurl.sty}{\usepackage{xurl}}{} % add URL line breaks if available
\urlstyle{same} % disable monospaced font for URLs
\hypersetup{
  hidelinks,
  pdfcreator={LaTeX via pandoc}}

\author{}
\date{}

\begin{document}

\emph{\textbf{D´edicace}}

\emph{Avec toute ma reconnaissance, je d´edie ce projet de fin d'´etudes
` a :}

\emph{\textbf{A mes chers parents, Tarak et Layla `}}

\emph{Vous ˆetes mon tr´esor. Aucun hommage ne saurait ˆetre ` a la
hauteur de l'amour dont vous}

\emph{m'avez toujours combl´e. Vous avez tout fait pour mon bonheur et
ma r´eussite. Merci d'ˆetre,}

\emph{tout simplement, mes parents.}

\begin{quote}
\emph{\textbf{A l'ˆ` ame de mes grands-m` eres}}
\end{quote}

\emph{Vous ˆetes toujours pr´esentes pour moi et vous resterez ` a
jamais dans mon cœur.}

\begin{quote}
\emph{\textbf{A mes chers fr` `}} \emph{\textbf{eres, Farah et Firas}}
\end{quote}

\emph{Vous avez ´et´e mes anges gardiens, toujours l` a pour me soutenir
dans les moments difficiles,}

\emph{de solitude et de doute. Merci d'ˆetre constamment ` a mes
cˆot´es, par votre pr´esence, votre}

\emph{amour d´evou´e et votre tendresse.}

\emph{\textbf{A tous les membres de ma famille `}}

\emph{Qui n'ont jamais cess´e de croire en moi.}

\begin{quote}
\emph{\textbf{A tous mes enseignants, du primaire ` `}} \emph{\textbf{a
l'universit´}}
\end{quote}

\emph{Pour leurs efforts qui ont contribu´e ` a ma r´eussite. Quels que
soient les mots employ´es, je}

\emph{n'arriverai jamais ` a exprimer toute ma gratitude sinc` ere.}

\emph{\textbf{A mes meilleur(e)s ami(e)s `}}

\emph{Pour leur soutien, leurs encouragements, les beaux moments
partag´es et les merveilleux}

\emph{souvenirs que je garde pr´ecieusement.}

\emph{\textbf{A tous ceux qui m'ont soutenu... `}}

i

\emph{\textbf{Remerciements}}

Je tiens ` a remercier Dieu, le Tout-Puissant, de m'avoir donn´e la
force, la sagesse et la patience

n´ecessaires pour mener ` a bien ce travail.

Ce stage ne se limite pas ` a une simple ´etape acad´emique, mais
constitue ´egalement une

exp´erience enrichissante, rendue possible grˆace ` a l'encadrement de
nombreuses personnes.

J'aimerais exprimer ma sinc` ere gratitude ` a tous ceux qui m'ont
soutenu tout au long de cette

p´eriode.

Je remercie tout d'abord l'ensemble de l'´equipe p´edagogique de l'IIT
ainsi que les intervenants

professionnels responsables de la formation, pour avoir assur´e la
partie th´eorique de mon

projet.

Je tiens ` a adresser mes remerciements les plus sinc` eres ` a Monsieur
\textbf{Mohamed KOUBAA},

mon encadrant acad´emique, pour son aide pr´ecieuse, sa disponibilit´e,
ses conseils avis´es et son

encouragement constant.

Mes remerciements vont ´egalement ` a Monsieur \textbf{Hassen Knani},
mon encadrant industriel,

pour la qualit´e de son encadrement et ses conseils pertinents. Sa
bienveillance, sa patience et

le temps qu'il m'a consacr´e ont ´et´e d'une grande valeur.

Je remercie aussi Monsieur \textbf{Mohamed Elfakhfekh}, CEO de la
soci´et´e Zetabox, pour son ac-

cueil chaleureux et sa collaboration en tant que \emph{Product Owner}
tout au long de mon stage. Ses

directives, ses conseils et ses remarques m'ont ´et´e extrˆemement
b´en´efiques. Je tiens ´egalement

a remercier Madame \textbf{Madiha Neifar}, Quality Manager, pour son
soutien et son accompagne-

ment pr´ecieux durant cette p´eriode. J'exprime ´egalement toute ma
reconnaissance envers le

personnel de \textbf{Zetabox Services} pour leur collaboration, leur
sympathie, leur esprit d'´equipe

et leur aide ` a mon int´egration.

Enfin, je remercie tous les membres du jury pour l'honneur qu'ils me
font en examinant mon

travail.

Ben Ayed Ilef

ii

\textbf{Table des mati` eres}

\textbf{Liste des figures} \textbf{ix}

\textbf{Liste des tableaux} \textbf{x}

\textbf{Introduction G´en´erale 1}

\textbf{1 Etude pr´ealable 3}

\begin{quote}
1.1 Introduction . . . . . . . . . . . . . . . . . . . . . . . . . . . .
. . . . . . . . . 4

1.2 Contexte du projet . . . . . . . . . . . . . . . . . . . . . . . . .
. . . . . . . . 4

1.3 Organisme d'accueil . . . . . . . . . . . . . . . . . . . . . . . .
. . . . . . . . . 5

1.3.1 Pr´esentation G´en´erale de l'entreprise ZETABOX . . . . . . . . .
. . . . 5

1.3.2 Fiche Signal´etique . . . . . . . . . . . . . . . . . . . . . . .
. . . . . . 6

1.3.3 Diff´erents services . . . . . . . . . . . . . . . . . . . . . . .
. . . . . . . 6

1.3.4 Organigramme de l'entreprise ZETABOX . . . . . . . . . . . . . . .
. . 7

1.4 D´efinition de la mission . . . . . . . . . . . . . . . . . . . . .
. . . . . . . . . 8

1.4.1 Cadre de travail . . . . . . . . . . . . . . . . . . . . . . . . .
. . . . . . 8

1.4.2 Pr´esentation du projet . . . . . . . . . . . . . . . . . . . . .
. . . . . . 8

1.4.3 Objectifs ` a atteindre . . . . . . . . . . . . . . . . . . . . .
. . . . . . . 8

1.5 Probl´ematique . . . . . . . . . . . . . . . . . . . . . . . . . . .
. . . . . . . . . 9

1.6 Etude de l'existant . . . . . . . . . . . . . . . . . . . . . . . .
. . . . . . . . .´ 9

1.6.1 Outils actuellement utilis´es . . . . . . . . . . . . . . . . . .
. . . . . . . 9

1.6.2 Limites et probl´ematiques identifi´ees . . . . . . . . . . . . .
. . . . . . 10

1.7 Solution propos´ee . . . . . . . . . . . . . . . . . . . . . . . . .
. . . . . . . . . 11

1.8 M´ethodologie de travail . . . . . . . . . . . . . . . . . . . . . .
. . . . . . . . . 12

1.8.1 Planification d'un projet SCRUM . . . . . . . . . . . . . . . . .
. . . . 14

1.8.2 Outil de planification du travail . . . . . . . . . . . . . . . .
. . . . . . 15

1.8.3 Planification pr´evisionnelle . . . . . . . . . . . . . . . . . .
. . . . . . . 16
\end{quote}

iii

\begin{longtable}[]{@{}
  >{\raggedright\arraybackslash}p{(\columnwidth - 22\tabcolsep) * \real{0.0833}}
  >{\raggedright\arraybackslash}p{(\columnwidth - 22\tabcolsep) * \real{0.0833}}
  >{\raggedright\arraybackslash}p{(\columnwidth - 22\tabcolsep) * \real{0.0833}}
  >{\raggedright\arraybackslash}p{(\columnwidth - 22\tabcolsep) * \real{0.0833}}
  >{\raggedright\arraybackslash}p{(\columnwidth - 22\tabcolsep) * \real{0.0833}}
  >{\raggedright\arraybackslash}p{(\columnwidth - 22\tabcolsep) * \real{0.0833}}
  >{\raggedright\arraybackslash}p{(\columnwidth - 22\tabcolsep) * \real{0.0833}}
  >{\raggedright\arraybackslash}p{(\columnwidth - 22\tabcolsep) * \real{0.0833}}
  >{\raggedright\arraybackslash}p{(\columnwidth - 22\tabcolsep) * \real{0.0833}}
  >{\raggedright\arraybackslash}p{(\columnwidth - 22\tabcolsep) * \real{0.0833}}
  >{\raggedright\arraybackslash}p{(\columnwidth - 22\tabcolsep) * \real{0.0833}}
  >{\raggedright\arraybackslash}p{(\columnwidth - 22\tabcolsep) * \real{0.0833}}@{}}
\toprule()
\multirow{2}{*}{\begin{minipage}[b]{\linewidth}\raggedright
\textbf{2}
\end{minipage}} & \begin{minipage}[b]{\linewidth}\raggedright
1.9
\end{minipage} &
\multicolumn{9}{>{\raggedright\arraybackslash}p{(\columnwidth - 22\tabcolsep) * \real{0.7500} + 16\tabcolsep}}{%
\begin{minipage}[b]{\linewidth}\raggedright
Conclusion . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . .
. . . . . . .
\end{minipage}} & \begin{minipage}[b]{\linewidth}\raggedright
17
\end{minipage} \\
&
\multicolumn{10}{>{\raggedright\arraybackslash}p{(\columnwidth - 22\tabcolsep) * \real{0.8333} + 18\tabcolsep}}{%
\begin{minipage}[b]{\linewidth}\raggedright
\begin{quote}
\textbf{Analyse et sp´ecification des besoins}
\end{quote}
\end{minipage}} & \begin{minipage}[b]{\linewidth}\raggedright
\textbf{18}
\end{minipage} \\
\midrule()
\endhead
\multirow{32}{*}{\textbf{3}} & 2.1 &
\multicolumn{9}{>{\raggedright\arraybackslash}p{(\columnwidth - 22\tabcolsep) * \real{0.7500} + 16\tabcolsep}}{%
Introduction . . . . . . . . . . . . . . . . . . . . . . . . . . . . . .
. . . . . . .} & 19 \\
& 2.2 &
\multicolumn{9}{>{\raggedright\arraybackslash}p{(\columnwidth - 22\tabcolsep) * \real{0.7500} + 16\tabcolsep}}{%
Sp´ecification des besoins . . . . . . . . . . . . . . . . . . . . . . .
. . . . . . .} & 19 \\
&
\multicolumn{2}{>{\raggedright\arraybackslash}p{(\columnwidth - 22\tabcolsep) * \real{0.1667} + 2\tabcolsep}}{%
2.2.1} &
\multicolumn{8}{>{\raggedright\arraybackslash}p{(\columnwidth - 22\tabcolsep) * \real{0.6667} + 14\tabcolsep}}{%
Identification des acteurs . . . . . . . . . . . . . . . . . . . . . . .
. . .} & 19 \\
&
\multicolumn{2}{>{\raggedright\arraybackslash}p{(\columnwidth - 22\tabcolsep) * \real{0.1667} + 2\tabcolsep}}{%
2.2.2} &
\multicolumn{8}{>{\raggedright\arraybackslash}p{(\columnwidth - 22\tabcolsep) * \real{0.6667} + 14\tabcolsep}}{%
Identification des besoins fonctionnels . . . . . . . . . . . . . . . .
. . .} & 20 \\
&
\multicolumn{2}{>{\raggedright\arraybackslash}p{(\columnwidth - 22\tabcolsep) * \real{0.1667} + 2\tabcolsep}}{%
2.2.3} &
\multicolumn{8}{>{\raggedright\arraybackslash}p{(\columnwidth - 22\tabcolsep) * \real{0.6667} + 14\tabcolsep}}{%
Identification des besoins non fonctionnels . . . . . . . . . . . . . .
. .} & 22 \\
& 2.3 &
\multicolumn{9}{>{\raggedright\arraybackslash}p{(\columnwidth - 22\tabcolsep) * \real{0.7500} + 16\tabcolsep}}{%
Pilotage du projet avec Scrum . . . . . . . . . . . . . . . . . . . . .
. . . . . .} & 23 \\
&
\multicolumn{2}{>{\raggedright\arraybackslash}p{(\columnwidth - 22\tabcolsep) * \real{0.1667} + 2\tabcolsep}}{%
2.3.1} &
\multicolumn{8}{>{\raggedright\arraybackslash}p{(\columnwidth - 22\tabcolsep) * \real{0.6667} + 14\tabcolsep}}{%
Fonctionnalit´es du Backlog . . . . . . . . . . . . . . . . . . . . . .
. . .} & 23 \\
&
\multicolumn{2}{>{\raggedright\arraybackslash}p{(\columnwidth - 22\tabcolsep) * \real{0.1667} + 2\tabcolsep}}{%
2.3.2} &
\multicolumn{5}{>{\raggedright\arraybackslash}p{(\columnwidth - 22\tabcolsep) * \real{0.4167} + 8\tabcolsep}}{%
Diagramme de cas d'utilisation} &
\multicolumn{3}{>{\raggedright\arraybackslash}p{(\columnwidth - 22\tabcolsep) * \real{0.2500} + 4\tabcolsep}}{%
. . . . . . . . . . . . . . . . . . . . . .} & 23 \\
&
\multicolumn{2}{>{\raggedright\arraybackslash}p{(\columnwidth - 22\tabcolsep) * \real{0.1667} + 2\tabcolsep}}{%
2.3.3} &
\multicolumn{4}{>{\raggedright\arraybackslash}p{(\columnwidth - 22\tabcolsep) * \real{0.3333} + 6\tabcolsep}}{%
Planification des sprints} &
\multicolumn{4}{>{\raggedright\arraybackslash}p{(\columnwidth - 22\tabcolsep) * \real{0.3333} + 6\tabcolsep}}{%
\begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
. . . . . . . . . . . . . . . . . . . . . . . . . .
\end{quote}
\end{minipage}} & 25 \\
& 2.4 &
\multicolumn{9}{>{\raggedright\arraybackslash}p{(\columnwidth - 22\tabcolsep) * \real{0.7500} + 16\tabcolsep}}{%
Architecture de la solution . . . . . . . . . . . . . . . . . . . . . .
. . . . . . .} & 26 \\
&
\multicolumn{2}{>{\raggedright\arraybackslash}p{(\columnwidth - 22\tabcolsep) * \real{0.1667} + 2\tabcolsep}}{%
2.4.1} &
\multicolumn{8}{>{\raggedright\arraybackslash}p{(\columnwidth - 22\tabcolsep) * \real{0.6667} + 14\tabcolsep}}{%
Architecture globale de la solution . . . . . . . . . . . . . . . . . .
. . .} & 26 \\
&
\multicolumn{2}{>{\raggedright\arraybackslash}p{(\columnwidth - 22\tabcolsep) * \real{0.1667} + 2\tabcolsep}}{%
2.4.2} &
\multicolumn{6}{>{\raggedright\arraybackslash}p{(\columnwidth - 22\tabcolsep) * \real{0.5000} + 10\tabcolsep}}{%
Architecture logique de la solution} &
\multicolumn{2}{>{\raggedright\arraybackslash}p{(\columnwidth - 22\tabcolsep) * \real{0.1667} + 2\tabcolsep}}{%
. . . . . . . . . . . . . . . . . . . .} & 28 \\
& 2.5 &
\multicolumn{9}{>{\raggedright\arraybackslash}p{(\columnwidth - 22\tabcolsep) * \real{0.7500} + 16\tabcolsep}}{%
Environnement de travail . . . . . . . . . . . . . . . . . . . . . . . .
. . . . . .} & 28 \\
&
\multicolumn{2}{>{\raggedright\arraybackslash}p{(\columnwidth - 22\tabcolsep) * \real{0.1667} + 2\tabcolsep}}{%
2.5.1} &
\multicolumn{4}{>{\raggedright\arraybackslash}p{(\columnwidth - 22\tabcolsep) * \real{0.3333} + 6\tabcolsep}}{%
Environnement mat´eriel} &
\multicolumn{4}{>{\raggedright\arraybackslash}p{(\columnwidth - 22\tabcolsep) * \real{0.3333} + 6\tabcolsep}}{%
\begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
. . . . . . . . . . . . . . . . . . . . . . . . . .
\end{quote}
\end{minipage}} & 28 \\
&
\multicolumn{2}{>{\raggedright\arraybackslash}p{(\columnwidth - 22\tabcolsep) * \real{0.1667} + 2\tabcolsep}}{%
2.5.2} &
\multicolumn{8}{>{\raggedright\arraybackslash}p{(\columnwidth - 22\tabcolsep) * \real{0.6667} + 14\tabcolsep}}{%
Environnement de d´eveloppement . . . . . . . . . . . . . . . . . . . .
.} & 29 \\
&
\multicolumn{2}{>{\raggedright\arraybackslash}p{(\columnwidth - 22\tabcolsep) * \real{0.1667} + 2\tabcolsep}}{%
2.5.3} &
\multicolumn{8}{>{\raggedright\arraybackslash}p{(\columnwidth - 22\tabcolsep) * \real{0.6667} + 14\tabcolsep}}{%
Environnement logiciel . . . . . . . . . . . . . . . . . . . . . . . . .
. .} & 30 \\
& 2.6 &
\multicolumn{9}{>{\raggedright\arraybackslash}p{(\columnwidth - 22\tabcolsep) * \real{0.7500} + 16\tabcolsep}}{%
Conclusion . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . .
. . . . . . .} & 32 \\
&
\multicolumn{10}{>{\raggedright\arraybackslash}p{(\columnwidth - 22\tabcolsep) * \real{0.8333} + 18\tabcolsep}}{%
\begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
\textbf{Authentification et param` etres administratifs}
\end{quote}
\end{minipage}} & \textbf{33} \\
& 3.1 &
\multicolumn{9}{>{\raggedright\arraybackslash}p{(\columnwidth - 22\tabcolsep) * \real{0.7500} + 16\tabcolsep}}{%
Introduction . . . . . . . . . . . . . . . . . . . . . . . . . . . . . .
. . . . . . .} & 34 \\
& 3.2 &
\multicolumn{3}{>{\raggedright\arraybackslash}p{(\columnwidth - 22\tabcolsep) * \real{0.2500} + 4\tabcolsep}}{%
Backlog du sprint 1} &
\multicolumn{6}{>{\raggedright\arraybackslash}p{(\columnwidth - 22\tabcolsep) * \real{0.5000} + 10\tabcolsep}}{%
. . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . .} & 34 \\
& 3.3 &
\multicolumn{4}{>{\raggedright\arraybackslash}p{(\columnwidth - 22\tabcolsep) * \real{0.3333} + 6\tabcolsep}}{%
Sp´ecification fonctionnelle} &
\multicolumn{5}{>{\raggedright\arraybackslash}p{(\columnwidth - 22\tabcolsep) * \real{0.4167} + 8\tabcolsep}}{%
. . . . . . . . . . . . . . . . . . . . . . . . . . . . .} & 36 \\
&
\multicolumn{2}{>{\raggedright\arraybackslash}p{(\columnwidth - 22\tabcolsep) * \real{0.1667} + 2\tabcolsep}}{%
3.3.1} &
\multicolumn{7}{>{\raggedright\arraybackslash}p{(\columnwidth - 22\tabcolsep) * \real{0.5833} + 12\tabcolsep}}{%
Diagramme de cas d'utilisation du premier sprint} & . . . . . . . . . .
. . & 36 \\
&
\multicolumn{2}{>{\raggedright\arraybackslash}p{(\columnwidth - 22\tabcolsep) * \real{0.1667} + 2\tabcolsep}}{%
3.3.2} &
\multicolumn{8}{>{\raggedright\arraybackslash}p{(\columnwidth - 22\tabcolsep) * \real{0.6667} + 14\tabcolsep}}{%
Description textuelle . . . . . . . . . . . . . . . . . . . . . . . . .
. . .} & 37 \\
& 3.4 &
\multicolumn{2}{>{\raggedright\arraybackslash}p{(\columnwidth - 22\tabcolsep) * \real{0.1667} + 2\tabcolsep}}{%
Conception} &
\multicolumn{7}{>{\raggedright\arraybackslash}p{(\columnwidth - 22\tabcolsep) * \real{0.5833} + 12\tabcolsep}}{%
. . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . .
.} & 39 \\
&
\multicolumn{2}{>{\raggedright\arraybackslash}p{(\columnwidth - 22\tabcolsep) * \real{0.1667} + 2\tabcolsep}}{%
3.4.1} &
\multicolumn{4}{>{\raggedright\arraybackslash}p{(\columnwidth - 22\tabcolsep) * \real{0.3333} + 6\tabcolsep}}{%
\begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
Diagrammes de s´equences
\end{quote}
\end{minipage}} &
\multicolumn{4}{>{\raggedright\arraybackslash}p{(\columnwidth - 22\tabcolsep) * \real{0.3333} + 6\tabcolsep}}{%
\begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
. . . . . . . . . . . . . . . . . . . . . . . . .
\end{quote}
\end{minipage}} & 40 \\
&
\multicolumn{2}{>{\raggedright\arraybackslash}p{(\columnwidth - 22\tabcolsep) * \real{0.1667} + 2\tabcolsep}}{%
3.4.2} &
\multicolumn{8}{>{\raggedright\arraybackslash}p{(\columnwidth - 22\tabcolsep) * \real{0.6667} + 14\tabcolsep}}{%
Diagramme de classes du sprint 1 . . . . . . . . . . . . . . . . . . . .
.} & 42 \\
& 3.5 &
\multicolumn{9}{>{\raggedright\arraybackslash}p{(\columnwidth - 22\tabcolsep) * \real{0.7500} + 16\tabcolsep}}{%
R´ealisation . . . . . . . . . . . . . . . . . . . . . . . . . . . . . .
. . . . . . . .} & 43 \\
&
\multicolumn{2}{>{\raggedright\arraybackslash}p{(\columnwidth - 22\tabcolsep) * \real{0.1667} + 2\tabcolsep}}{%
3.5.1} &
\multicolumn{8}{>{\raggedright\arraybackslash}p{(\columnwidth - 22\tabcolsep) * \real{0.6667} + 14\tabcolsep}}{%
Param` etres administrative . . . . . . . . . . . . . . . . . . . . . .
. . .} & 43 \\
&
\multicolumn{2}{>{\raggedright\arraybackslash}p{(\columnwidth - 22\tabcolsep) * \real{0.1667} + 2\tabcolsep}}{%
3.5.2} &
\multicolumn{8}{>{\raggedright\arraybackslash}p{(\columnwidth - 22\tabcolsep) * \real{0.6667} + 14\tabcolsep}}{%
G´erer profile . . . . . . . . . . . . . . . . . . . . . . . . . . . . .
. . . .} & 46 \\
& 3.6 &
\multicolumn{9}{>{\raggedright\arraybackslash}p{(\columnwidth - 22\tabcolsep) * \real{0.7500} + 16\tabcolsep}}{%
Test et validation . . . . . . . . . . . . . . . . . . . . . . . . . . .
. . . . . . .} & 48 \\
&
\multicolumn{2}{>{\raggedright\arraybackslash}p{(\columnwidth - 22\tabcolsep) * \real{0.1667} + 2\tabcolsep}}{%
3.6.1} &
\multicolumn{8}{>{\raggedright\arraybackslash}p{(\columnwidth - 22\tabcolsep) * \real{0.6667} + 14\tabcolsep}}{%
Test unitaire . . . . . . . . . . . . . . . . . . . . . . . . . . . . .
. . . .} & 48 \\
&
\multicolumn{2}{>{\raggedright\arraybackslash}p{(\columnwidth - 22\tabcolsep) * \real{0.1667} + 2\tabcolsep}}{%
3.6.2} &
\multicolumn{8}{>{\raggedright\arraybackslash}p{(\columnwidth - 22\tabcolsep) * \real{0.6667} + 14\tabcolsep}}{%
Tests unitaires avec Jest . . . . . . . . . . . . . . . . . . . . . . .
. . .} & 48 \\
\bottomrule()
\end{longtable}

iv

\begin{longtable}[]{@{}
  >{\raggedright\arraybackslash}p{(\columnwidth - 22\tabcolsep) * \real{0.0833}}
  >{\raggedright\arraybackslash}p{(\columnwidth - 22\tabcolsep) * \real{0.0833}}
  >{\raggedright\arraybackslash}p{(\columnwidth - 22\tabcolsep) * \real{0.0833}}
  >{\raggedright\arraybackslash}p{(\columnwidth - 22\tabcolsep) * \real{0.0833}}
  >{\raggedright\arraybackslash}p{(\columnwidth - 22\tabcolsep) * \real{0.0833}}
  >{\raggedright\arraybackslash}p{(\columnwidth - 22\tabcolsep) * \real{0.0833}}
  >{\raggedright\arraybackslash}p{(\columnwidth - 22\tabcolsep) * \real{0.0833}}
  >{\raggedright\arraybackslash}p{(\columnwidth - 22\tabcolsep) * \real{0.0833}}
  >{\raggedright\arraybackslash}p{(\columnwidth - 22\tabcolsep) * \real{0.0833}}
  >{\raggedright\arraybackslash}p{(\columnwidth - 22\tabcolsep) * \real{0.0833}}
  >{\raggedright\arraybackslash}p{(\columnwidth - 22\tabcolsep) * \real{0.0833}}
  >{\raggedright\arraybackslash}p{(\columnwidth - 22\tabcolsep) * \real{0.0833}}@{}}
\toprule()
\multirow{2}{*}{\begin{minipage}[b]{\linewidth}\raggedright
\textbf{4}
\end{minipage}} & \begin{minipage}[b]{\linewidth}\raggedright
3.7
\end{minipage} &
\multicolumn{9}{>{\raggedright\arraybackslash}p{(\columnwidth - 22\tabcolsep) * \real{0.7500} + 16\tabcolsep}}{%
\begin{minipage}[b]{\linewidth}\raggedright
Conclusion . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . .
. . . . . . .
\end{minipage}} & \begin{minipage}[b]{\linewidth}\raggedright
48
\end{minipage} \\
&
\multicolumn{10}{>{\raggedright\arraybackslash}p{(\columnwidth - 22\tabcolsep) * \real{0.8333} + 18\tabcolsep}}{%
\begin{minipage}[b]{\linewidth}\raggedright
\begin{quote}
\textbf{Gestion op´erationnelle et financi` ere des projets dans
Pro-Tracker}
\end{quote}
\end{minipage}} & \begin{minipage}[b]{\linewidth}\raggedright
\textbf{49}
\end{minipage} \\
\midrule()
\endhead
\multirow{32}{*}{\textbf{5}} & 4.1 &
\multicolumn{9}{>{\raggedright\arraybackslash}p{(\columnwidth - 22\tabcolsep) * \real{0.7500} + 16\tabcolsep}}{%
Introduction . . . . . . . . . . . . . . . . . . . . . . . . . . . . . .
. . . . . . .} & 50 \\
& 4.2 &
\multicolumn{3}{>{\raggedright\arraybackslash}p{(\columnwidth - 22\tabcolsep) * \real{0.2500} + 4\tabcolsep}}{%
Backlog du sprint 2} &
\multicolumn{6}{>{\raggedright\arraybackslash}p{(\columnwidth - 22\tabcolsep) * \real{0.5000} + 10\tabcolsep}}{%
. . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . .} & 50 \\
& 4.3 &
\multicolumn{4}{>{\raggedright\arraybackslash}p{(\columnwidth - 22\tabcolsep) * \real{0.3333} + 6\tabcolsep}}{%
Sp´ecification fonctionnelle} &
\multicolumn{5}{>{\raggedright\arraybackslash}p{(\columnwidth - 22\tabcolsep) * \real{0.4167} + 8\tabcolsep}}{%
. . . . . . . . . . . . . . . . . . . . . . . . . . . . .} & 53 \\
&
\multicolumn{2}{>{\raggedright\arraybackslash}p{(\columnwidth - 22\tabcolsep) * \real{0.1667} + 2\tabcolsep}}{%
4.3.1} &
\multicolumn{7}{>{\raggedright\arraybackslash}p{(\columnwidth - 22\tabcolsep) * \real{0.5833} + 12\tabcolsep}}{%
Diagramme de cas d'utilisation du deuxi´eme sprint} & . . . . . . . . .
. . & 53 \\
&
\multicolumn{2}{>{\raggedright\arraybackslash}p{(\columnwidth - 22\tabcolsep) * \real{0.1667} + 2\tabcolsep}}{%
4.3.2} &
\multicolumn{8}{>{\raggedright\arraybackslash}p{(\columnwidth - 22\tabcolsep) * \real{0.6667} + 14\tabcolsep}}{%
Description textuelle . . . . . . . . . . . . . . . . . . . . . . . . .
. . .} & 54 \\
& 4.4 &
\multicolumn{2}{>{\raggedright\arraybackslash}p{(\columnwidth - 22\tabcolsep) * \real{0.1667} + 2\tabcolsep}}{%
Conception} &
\multicolumn{7}{>{\raggedright\arraybackslash}p{(\columnwidth - 22\tabcolsep) * \real{0.5833} + 12\tabcolsep}}{%
. . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . .
.} & 58 \\
&
\multicolumn{2}{>{\raggedright\arraybackslash}p{(\columnwidth - 22\tabcolsep) * \real{0.1667} + 2\tabcolsep}}{%
4.4.1} &
\multicolumn{4}{>{\raggedright\arraybackslash}p{(\columnwidth - 22\tabcolsep) * \real{0.3333} + 6\tabcolsep}}{%
\begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
Diagrammes de s´equences
\end{quote}
\end{minipage}} &
\multicolumn{4}{>{\raggedright\arraybackslash}p{(\columnwidth - 22\tabcolsep) * \real{0.3333} + 6\tabcolsep}}{%
\begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
. . . . . . . . . . . . . . . . . . . . . . . . .
\end{quote}
\end{minipage}} & 58 \\
&
\multicolumn{2}{>{\raggedright\arraybackslash}p{(\columnwidth - 22\tabcolsep) * \real{0.1667} + 2\tabcolsep}}{%
4.4.2} &
\multicolumn{8}{>{\raggedright\arraybackslash}p{(\columnwidth - 22\tabcolsep) * \real{0.6667} + 14\tabcolsep}}{%
Diagramme de classes du sprint 2 . . . . . . . . . . . . . . . . . . . .
.} & 60 \\
& 4.5 &
\multicolumn{9}{>{\raggedright\arraybackslash}p{(\columnwidth - 22\tabcolsep) * \real{0.7500} + 16\tabcolsep}}{%
R´ealisation . . . . . . . . . . . . . . . . . . . . . . . . . . . . . .
. . . . . . . .} & 61 \\
&
\multicolumn{2}{>{\raggedright\arraybackslash}p{(\columnwidth - 22\tabcolsep) * \real{0.1667} + 2\tabcolsep}}{%
4.5.1} &
\multicolumn{8}{>{\raggedright\arraybackslash}p{(\columnwidth - 22\tabcolsep) * \real{0.6667} + 14\tabcolsep}}{%
\begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
Interface consulter projet . . . . . . . . . . . . . . . . . . . . . . .
. .
\end{quote}
\end{minipage}} & 62 \\
&
\multicolumn{2}{>{\raggedright\arraybackslash}p{(\columnwidth - 22\tabcolsep) * \real{0.1667} + 2\tabcolsep}}{%
4.5.2} &
\multicolumn{8}{>{\raggedright\arraybackslash}p{(\columnwidth - 22\tabcolsep) * \real{0.6667} + 14\tabcolsep}}{%
Interface tableau de board . . . . . . . . . . . . . . . . . . . . . . .
. .} & 62 \\
&
\multicolumn{2}{>{\raggedright\arraybackslash}p{(\columnwidth - 22\tabcolsep) * \real{0.1667} + 2\tabcolsep}}{%
4.5.3} &
\multicolumn{8}{>{\raggedright\arraybackslash}p{(\columnwidth - 22\tabcolsep) * \real{0.6667} + 14\tabcolsep}}{%
Interface liste des tˆaches . . . . . . . . . . . . . . . . . . . . . .
. . . .} & 63 \\
&
\multicolumn{2}{>{\raggedright\arraybackslash}p{(\columnwidth - 22\tabcolsep) * \real{0.1667} + 2\tabcolsep}}{%
4.5.4} &
\multicolumn{8}{>{\raggedright\arraybackslash}p{(\columnwidth - 22\tabcolsep) * \real{0.6667} + 14\tabcolsep}}{%
Interface liste des bugs . . . . . . . . . . . . . . . . . . . . . . . .
. . .} & 63 \\
&
\multicolumn{2}{>{\raggedright\arraybackslash}p{(\columnwidth - 22\tabcolsep) * \real{0.1667} + 2\tabcolsep}}{%
4.5.5} &
\multicolumn{5}{>{\raggedright\arraybackslash}p{(\columnwidth - 22\tabcolsep) * \real{0.4167} + 8\tabcolsep}}{%
Interface des membres du projet} &
\multicolumn{3}{>{\raggedright\arraybackslash}p{(\columnwidth - 22\tabcolsep) * \real{0.2500} + 4\tabcolsep}}{%
. . . . . . . . . . . . . . . . . . . . .} & 64 \\
&
\multicolumn{2}{>{\raggedright\arraybackslash}p{(\columnwidth - 22\tabcolsep) * \real{0.1667} + 2\tabcolsep}}{%
4.5.6} &
\multicolumn{6}{>{\raggedright\arraybackslash}p{(\columnwidth - 22\tabcolsep) * \real{0.5000} + 10\tabcolsep}}{%
Interface des fichiers et dossiers d'un projet} &
\multicolumn{2}{>{\raggedright\arraybackslash}p{(\columnwidth - 22\tabcolsep) * \real{0.1667} + 2\tabcolsep}}{%
. . . . . . . . . . . . . . .} & 64 \\
& 4.6 &
\multicolumn{9}{>{\raggedright\arraybackslash}p{(\columnwidth - 22\tabcolsep) * \real{0.7500} + 16\tabcolsep}}{%
Test et validation . . . . . . . . . . . . . . . . . . . . . . . . . . .
. . . . . . .} & 65 \\
&
\multicolumn{2}{>{\raggedright\arraybackslash}p{(\columnwidth - 22\tabcolsep) * \real{0.1667} + 2\tabcolsep}}{%
4.6.1} &
\multicolumn{8}{>{\raggedright\arraybackslash}p{(\columnwidth - 22\tabcolsep) * \real{0.6667} + 14\tabcolsep}}{%
Test unitaire . . . . . . . . . . . . . . . . . . . . . . . . . . . . .
. . . .} & 65 \\
&
\multicolumn{2}{>{\raggedright\arraybackslash}p{(\columnwidth - 22\tabcolsep) * \real{0.1667} + 2\tabcolsep}}{%
4.6.2} &
\multicolumn{8}{>{\raggedright\arraybackslash}p{(\columnwidth - 22\tabcolsep) * \real{0.6667} + 14\tabcolsep}}{%
Tests unitaires avec Jest . . . . . . . . . . . . . . . . . . . . . . .
. . .} & 65 \\
& 4.7 &
\multicolumn{9}{>{\raggedright\arraybackslash}p{(\columnwidth - 22\tabcolsep) * \real{0.7500} + 16\tabcolsep}}{%
Conclusion . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . .
. . . . . . .} & 66 \\
&
\multicolumn{10}{>{\raggedright\arraybackslash}p{(\columnwidth - 22\tabcolsep) * \real{0.8333} + 18\tabcolsep}}{%
\begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
\textbf{Suivi et contrˆole de l'avancement des projets}
\end{quote}
\end{minipage}} & \textbf{67} \\
& 5.1 &
\multicolumn{9}{>{\raggedright\arraybackslash}p{(\columnwidth - 22\tabcolsep) * \real{0.7500} + 16\tabcolsep}}{%
Introduction . . . . . . . . . . . . . . . . . . . . . . . . . . . . . .
. . . . . . .} & 68 \\
& 5.2 &
\multicolumn{3}{>{\raggedright\arraybackslash}p{(\columnwidth - 22\tabcolsep) * \real{0.2500} + 4\tabcolsep}}{%
Backlog du sprint 3} &
\multicolumn{6}{>{\raggedright\arraybackslash}p{(\columnwidth - 22\tabcolsep) * \real{0.5000} + 10\tabcolsep}}{%
. . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . .} & 68 \\
& 5.3 &
\multicolumn{4}{>{\raggedright\arraybackslash}p{(\columnwidth - 22\tabcolsep) * \real{0.3333} + 6\tabcolsep}}{%
Sp´ecification fonctionnelle} &
\multicolumn{5}{>{\raggedright\arraybackslash}p{(\columnwidth - 22\tabcolsep) * \real{0.4167} + 8\tabcolsep}}{%
. . . . . . . . . . . . . . . . . . . . . . . . . . . . .} & 71 \\
&
\multicolumn{2}{>{\raggedright\arraybackslash}p{(\columnwidth - 22\tabcolsep) * \real{0.1667} + 2\tabcolsep}}{%
5.3.1} &
\multicolumn{7}{>{\raggedright\arraybackslash}p{(\columnwidth - 22\tabcolsep) * \real{0.5833} + 12\tabcolsep}}{%
Diagramme de cas d'utilisation du troixi` eme sprint} & . . . . . . . .
. . . & 71 \\
&
\multicolumn{2}{>{\raggedright\arraybackslash}p{(\columnwidth - 22\tabcolsep) * \real{0.1667} + 2\tabcolsep}}{%
5.3.2} &
\multicolumn{8}{>{\raggedright\arraybackslash}p{(\columnwidth - 22\tabcolsep) * \real{0.6667} + 14\tabcolsep}}{%
Desciption textuelle . . . . . . . . . . . . . . . . . . . . . . . . . .
. . .} & 73 \\
& 5.4 &
\multicolumn{2}{>{\raggedright\arraybackslash}p{(\columnwidth - 22\tabcolsep) * \real{0.1667} + 2\tabcolsep}}{%
Conception} &
\multicolumn{7}{>{\raggedright\arraybackslash}p{(\columnwidth - 22\tabcolsep) * \real{0.5833} + 12\tabcolsep}}{%
. . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . .
.} & 75 \\
&
\multicolumn{2}{>{\raggedright\arraybackslash}p{(\columnwidth - 22\tabcolsep) * \real{0.1667} + 2\tabcolsep}}{%
5.4.1} &
\multicolumn{4}{>{\raggedright\arraybackslash}p{(\columnwidth - 22\tabcolsep) * \real{0.3333} + 6\tabcolsep}}{%
Diagramme de s´equence} &
\multicolumn{4}{>{\raggedright\arraybackslash}p{(\columnwidth - 22\tabcolsep) * \real{0.3333} + 6\tabcolsep}}{%
\begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
. . . . . . . . . . . . . . . . . . . . . . . . . .
\end{quote}
\end{minipage}} & 75 \\
&
\multicolumn{2}{>{\raggedright\arraybackslash}p{(\columnwidth - 22\tabcolsep) * \real{0.1667} + 2\tabcolsep}}{%
5.4.2} &
\multicolumn{8}{>{\raggedright\arraybackslash}p{(\columnwidth - 22\tabcolsep) * \real{0.6667} + 14\tabcolsep}}{%
Diagramme de classes du sprint 3 . . . . . . . . . . . . . . . . . . . .
.} & 76 \\
& 5.5 &
\multicolumn{9}{>{\raggedright\arraybackslash}p{(\columnwidth - 22\tabcolsep) * \real{0.7500} + 16\tabcolsep}}{%
R´ealisation . . . . . . . . . . . . . . . . . . . . . . . . . . . . . .
. . . . . . . .} & 77 \\
&
\multicolumn{2}{>{\raggedright\arraybackslash}p{(\columnwidth - 22\tabcolsep) * \real{0.1667} + 2\tabcolsep}}{%
5.5.1} &
\multicolumn{8}{>{\raggedright\arraybackslash}p{(\columnwidth - 22\tabcolsep) * \real{0.6667} + 14\tabcolsep}}{%
Analyse des Milestones et KPIs et finance des Projets . . . . . . . . .
.} & 77 \\
&
\multicolumn{2}{>{\raggedright\arraybackslash}p{(\columnwidth - 22\tabcolsep) * \real{0.1667} + 2\tabcolsep}}{%
5.5.2} &
\multicolumn{8}{>{\raggedright\arraybackslash}p{(\columnwidth - 22\tabcolsep) * \real{0.6667} + 14\tabcolsep}}{%
Les membres et leurs d´etails . . . . . . . . . . . . . . . . . . . . .
. . .} & 79 \\
&
\multicolumn{2}{>{\raggedright\arraybackslash}p{(\columnwidth - 22\tabcolsep) * \real{0.1667} + 2\tabcolsep}}{%
5.5.3} &
\multicolumn{8}{>{\raggedright\arraybackslash}p{(\columnwidth - 22\tabcolsep) * \real{0.6667} + 14\tabcolsep}}{%
Chatbot intelligent . . . . . . . . . . . . . . . . . . . . . . . . . .
. . .} & 81 \\
\bottomrule()
\end{longtable}

v

\begin{quote}
5.6 Test et validation . . . . . . . . . . . . . . . . . . . . . . . . .
. . . . . . . . . 82

5.6.1 Test unitaire . . . . . . . . . . . . . . . . . . . . . . . . . .
. . . . . . . 82

5.6.2 Tests unitaires avec Jest . . . . . . . . . . . . . . . . . . . .
. . . . . . 83

5.7 Conslusion . . . . . . . . . . . . . . . . . . . . . . . . . . . . .
. . . . . . . . . 83
\end{quote}

\textbf{6 Yejoura : Fonctionnalit´es avanc´ees de gestion de projet}
\textbf{84}

\begin{quote}
6.1 Introduction . . . . . . . . . . . . . . . . . . . . . . . . . . . .
. . . . . . . . . 85

6.2 Backlog du sprint 4 . . . . . . . . . . . . . . . . . . . . . . . .
. . . . . . . . 85

6.3 Sp´ecification fonctionnelle . . . . . . . . . . . . . . . . . . . .
. . . . . . . . . 87

6.3.1 Diagramme de cas d'utilisation du quatri` eme sprint . . . . . . .
. . . . 87

6.3.2 Description textuelle . . . . . . . . . . . . . . . . . . . . . .
. . . . . . 89

6.4 Conception . . . . . . . . . . . . . . . . . . . . . . . . . . . . .
. . . . . . . . 91

6.4.1 Diagrammes de s´equences . . . . . . . . . . . . . . . . . . . . .
. . . . 91

6.4.2 Diagramme de classes du sprint 4 . . . . . . . . . . . . . . . . .
. . . . 93

6.5 R´ealisation . . . . . . . . . . . . . . . . . . . . . . . . . . . .
. . . . . . . . . . 95

6.5.1 Authentification . . . . . . . . . . . . . . . . . . . . . . . . .
. . . . . . 95

6.5.2 Gestion des projets et des membres . . . . . . . . . . . . . . . .
. . . . 97

6.6 Test et validation . . . . . . . . . . . . . . . . . . . . . . . . .
. . . . . . . . . 102

6.6.1 Test unitaire . . . . . . . . . . . . . . . . . . . . . . . . . .
. . . . . . . 102

6.6.2 Tests unitaires avec Jest . . . . . . . . . . . . . . . . . . . .
. . . . . . 102

6.7 Conclusion . . . . . . . . . . . . . . . . . . . . . . . . . . . . .
. . . . . . . . . 103
\end{quote}

\textbf{Conclusion et perspectives 104}

\textbf{Webographie 105}

vi

\textbf{Table des figures}

\begin{longtable}[]{@{}
  >{\raggedright\arraybackslash}p{(\columnwidth - 4\tabcolsep) * \real{0.3333}}
  >{\raggedright\arraybackslash}p{(\columnwidth - 4\tabcolsep) * \real{0.3333}}
  >{\raggedright\arraybackslash}p{(\columnwidth - 4\tabcolsep) * \real{0.3333}}@{}}
\toprule()
\begin{minipage}[b]{\linewidth}\raggedright
1.1
\end{minipage} & \begin{minipage}[b]{\linewidth}\raggedright
Logo ZetaBox . . . . . . . . . . . . . . . . . . . . . . . . . . . . . .
. . . . . .
\end{minipage} & \begin{minipage}[b]{\linewidth}\raggedright
5
\end{minipage} \\
\midrule()
\endhead
\bottomrule()
\end{longtable}

\begin{quote}
1.2 Organigramme de l'entreprise ZETABOX . . . . . . . . . . . . . . . .
. . . . . 7

1.3 M´ethodologie Scrum . . . . . . . . . . . . . . . . . . . . . . . .
. . . . . . . . 13

1.4 Cycle de vie de la m´ethodologie SCRUM . . . . . . . . . . . . . . .
. . . . . . 13

1.5 Cycle de vie de la m´ethode SCRUM Pro-tracker . . . . . . . . . . .
. . . . . . 15

1.6 Cycle de vie de la m´ethode SCRUM Yejoura . . . . . . . . . . . . .
. . . . . . 16

2.1 Diagramme de cas d'utilisation . . . . . . . . . . . . . . . . . . .
. . . . . . . 24

2.2 Planification des sprints . . . . . . . . . . . . . . . . . . . . .
. . . . . . . . . 26

2.3 Architecture globale de la solution . . . . . . . . . . . . . . . .
. . . . . . . . . 27

2.4 Logo Enterprise Architect . . . . . . . . . . . . . . . . . . . . .
. . . . . . . . 29

2.5 Logo Visual Studio Code . . . . . . . . . . . . . . . . . . . . . .
. . . . . . . . 29

2.6 Logo Postman . . . . . . . . . . . . . . . . . . . . . . . . . . . .
. . . . . . . . 29

2.7 Logo GitHub . . . . . . . . . . . . . . . . . . . . . . . . . . . .
. . . . . . . . 30

2.8 Logo PostgreSQL . . . . . . . . . . . . . . . . . . . . . . . . . .
. . . . . . . . 30

2.9 Logo Next.js . . . . . . . . . . . . . . . . . . . . . . . . . . . .
. . . . . . . . . 30

2.10 Logo Node.js . . . . . . . . . . . . . . . . . . . . . . . . . . .
. . . . . . . . . 30

2.11 Logo TypeScript . . . . . . . . . . . . . . . . . . . . . . . . . .
. . . . . . . . 31

2.12 Logo Ant Design . . . . . . . . . . . . . . . . . . . . . . . . . .
. . . . . . . . 31

2.13 Logo Tailwind CSS . . . . . . . . . . . . . . . . . . . . . . . . .
. . . . . . . . 31

2.14 Logo Python . . . . . . . . . . . . . . . . . . . . . . . . . . . .
. . . . . . . . 31

2.15 Logo Flask . . . . . . . . . . . . . . . . . . . . . . . . . . . .
. . . . . . . . . . 32

2.16 Logo Jest . . . . . . . . . . . . . . . . . . . . . . . . . . . . .
. . . . . . . . . 32

3.1 diagramme de cas d'utilisation du premier sprint . . . . . . . . . .
. . . . . . . 36

3.2 Diagramme de cas d'utilisation raffin´e :\emph{≪}G´erer les horaires
de travail\emph{≫}. . . . 37

3.3 Diagramme de cas d'utilisation raffin´e :\emph{≪}G´erer
profil\emph{≫}. . . . . . . . . . . . . 38
\end{quote}

vii

\begin{quote}
3.4 diagramme de s´equence de\emph{≪}Gestion des horaires de
travail\emph{≫}. . . . . . . . . . 40

3.5 Digramme de s´equence\emph{≪}G´erer profil\emph{≫}. . . . . . . . .
. . . . . . . . . . . . . . 41

3.6 Diagramme de classes du sprint 1 . . . . . . . . . . . . . . . . . .
. . . . . . . 42

3.7 Interface liste des utilisateurs . . . . . . . . . . . . . . . . . .
. . . . . . . . . 43

3.8 Interface inviter un utilisateur . . . . . . . . . . . . . . . . . .
. . . . . . . . . 44

3.9 Invitation par email . . . . . . . . . . . . . . . . . . . . . . . .
. . . . . . . . . 45

3.10 Interface liste des horaires de travail . . . . . . . . . . . . . .
. . . . . . . . . . 45

3.11 Interface ajouter horaires . . . . . . . . . . . . . . . . . . . .
. . . . . . . . . . 46

3.12 Interface modifier horaires . . . . . . . . . . . . . . . . . . . .
. . . . . . . . . 46

3.13 Interface informations sp´ecifiques . . . . . . . . . . . . . . . .
. . . . . . . . . 46

3.14 Interface modifier informations sp´ecifiques . . . . . . . . . . .
. . . . . . . . . 47

3.15 Interface Mot de passe oubli´ . . . . . . . . . . . . . . . . . . .
. . . . . . . . 47

3.16 Tests du composant WorkHours . . . . . . . . . . . . . . . . . . .
. . . . . . . 48

4.1 diagramme de cas d'utilisation du deuxi´eme sprint . . . . . . . . .
. . . . . . . 53

4.2 Diagramme de cas d'utilisation raffin´e :\emph{≪}G´erer les fichiers
d'un projet\emph{≫}. . . 54

4.3 Diagramme de cas d'utilisation raffin´e :\emph{≪}G´erer les fichiers
d'un projet\emph{≫}. . . 58

4.4 Diagramme de s´equence de cas d'utilisation consulter la liste des
projets . . . 59

4.5 Diagramme de classes du sprint 2 . . . . . . . . . . . . . . . . . .
. . . . . . . 60

4.6 Interface liste des projets . . . . . . . . . . . . . . . . . . . .
. . . . . . . . . . 62

4.7 Interface tableau de baord . . . . . . . . . . . . . . . . . . . . .
. . . . . . . . 62

4.8 Interface liste des tˆaches . . . . . . . . . . . . . . . . . . . .
. . . . . . . . . . 63

4.9 Interface liste des bugs . . . . . . . . . . . . . . . . . . . . . .
. . . . . . . . . 63

4.10 Interface des membres du projet . . . . . . . . . . . . . . . . . .
. . . . . . . . 64

4.11 Interface des fichiers d'un projet . . . . . . . . . . . . . . . .
. . . . . . . . . . 64

4.12 Tests du composant ProjectsPage . . . . . . . . . . . . . . . . . .
. . . . . . . 65

5.1 diagramme de cas d'utilisation du troixi` eme sprint . . . . . . . .
. . . . . . . . 71

5.2 Diagramme de cas d'utilisation raffin´e :\emph{≪}Gestion de
justification de pr´esence\emph{≫}72

5.3 Diagramme de cas d'utilisation raffin´e :\emph{≪}Gestion des
finances du projet\emph{≫}. . . 73

5.4 Diagramme de s´equence de cas d'utilisation ajouter une d´epense . .
. . . . . . 75

5.5 Diagramme de classes du sprint 3 . . . . . . . . . . . . . . . . . .
. . . . . . . 76

5.6 Interface de liste des Jalons . . . . . . . . . . . . . . . . . . .
. . . . . . . . . 78

5.7 Interface des indicateurs de performance . . . . . . . . . . . . . .
. . . . . . . 78

5.8 Interface de finance des projets . . . . . . . . . . . . . . . . . .
. . . . . . . . 79

5.9 Interface liste des membres . . . . . . . . . . . . . . . . . . . .
. . . . . . . . 79
\end{quote}

viii

\begin{quote}
5.10 Interface Suivi de pr´esence des membre . . . . . . . . . . . . . .
. . . . . . . . 80

5.11 Interface ajouter justification . . . . . . . . . . . . . . . . . .
. . . . . . . . . . 80

5.12 Interface modifier justification . . . . . . . . . . . . . . . . .
. . . . . . . . . . 80

5.13 Diagramme d'Architecture du syst` eme RAG (Retrieval-Augmented
Generation) 81

5.14 Interface du chatbot . . . . . . . . . . . . . . . . . . . . . . .
. . . . . . . . . 82

5.15 Tests du composant Attendance avec Jest . . . . . . . . . . . . . .
. . . . . . 83

6.1 diagramme de cas d'utilisation du quatri` eme sprint . . . . . . . .
. . . . . . . 88

6.2 Diagramme de cas d'utilisation raffin´e :\emph{≪}Gestion des
membres\emph{≫}. . . . . . . 89

6.3 diagramme de cas d'utilisation du quatri` eme sprint . . . . . . . .
. . . . . . . 92

6.4 Diagramme de s´equence\emph{≪}Inviter un utilisateur\emph{≫}. . . .
. . . . . . . . . . . . . 93

6.5 Diagramme de classe du sprint 4 . . . . . . . . . . . . . . . . . .
. . . . . . . . 94

6.6 Interfaces d'enregistrement et de connexion de la plateforme yejoura
. . . . . . 96

6.7 Interface Mot de passe oubli´ . . . . . . . . . . . . . . . . . . .
. . . . . . . . 96

6.8 Interface Modifier mot de passe . . . . . . . . . . . . . . . . . .
. . . . . . . . 96

6.9 Interface de liste des membres . . . . . . . . . . . . . . . . . . .
. . . . . . . . 97

6.10 Interface inviter un membre . . . . . . . . . . . . . . . . . . . .
. . . . . . . . 97

6.11 Interface ajouter un membre existant . . . . . . . . . . . . . . .
. . . . . . . . 97

6.12 Interface gantt chart . . . . . . . . . . . . . . . . . . . . . . .
. . . . . . . . . 98

6.13 Interface de mesure de la pr´ecision des estimations . . . . . . .
. . . . . . . . . 99

6.14 Interface de mesure du temps de cycle . . . . . . . . . . . . . . .
. . . . . . . 99

6.15 Interface de suivi du ratio de bugs . . . . . . . . . . . . . . . .
. . . . . . . . . 100

6.16 Interface du taux de compl´etion des projets . . . . . . . . . . .
. . . . . . . . 101

6.17 Interface de suivi du taux de livraison ` a temps . . . . . . . . .
. . . . . . . . . 101

6.18 Interface de d´etails des tˆaches . . . . . . . . . . . . . . . . .
. . . . . . . . . . 102

6.19 Tests du composant KPIs avec Jest . . . . . . . . . . . . . . . . .
. . . . . . . 102
\end{quote}

ix

\textbf{Liste des tableaux}

\begin{longtable}[]{@{}
  >{\raggedright\arraybackslash}p{(\columnwidth - 4\tabcolsep) * \real{0.3333}}
  >{\raggedright\arraybackslash}p{(\columnwidth - 4\tabcolsep) * \real{0.3333}}
  >{\raggedright\arraybackslash}p{(\columnwidth - 4\tabcolsep) * \real{0.3333}}@{}}
\toprule()
\begin{minipage}[b]{\linewidth}\raggedright
1.1
\end{minipage} & \begin{minipage}[b]{\linewidth}\raggedright
Informations g´en´erales sur l'entreprise ZETABOX . . . . . . . . . . .
. . . . .
\end{minipage} & \begin{minipage}[b]{\linewidth}\raggedright
6
\end{minipage} \\
\midrule()
\endhead
\bottomrule()
\end{longtable}

\begin{quote}
1.2 R´esum´e des outils actuels et de leurs limites . . . . . . . . . .
. . . . . . . . . 10

1.3 R´epartition des rˆoles dans l'´equipe Scrum . . . . . . . . . . . .
. . . . . . . . . 14

1.4 Planning des ´etapes du projet . . . . . . . . . . . . . . . . . . .
. . . . . . . . 16

2.1 Acteurs de l'application . . . . . . . . . . . . . . . . . . . . . .
. . . . . . . . 20

3.1 Backlog de sprint 1-- Authentification, gestion des profils et
param` etres admi-

nistratifs . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . .
. . . . . . . . 34

3.2 Description textuelle -- Gestion des horaires de travail . . . . . .
. . . . . . . . 37

3.3 Description textuelle -- Inviter utilisateurs . . . . . . . . . . .
. . . . . . . . . 38

4.1 Backlog de sprint 2- Gestion op´erationnelle et financi` ere des
projets dans Pro-

Tracker . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . .
. . . . . . . . 50

4.2 Description textuelle -- G´erer les fichiers d'un projet . . . . . .
. . . . . . . . . 54

4.3 Description textuelle -- Consulter la liste des projets . . . . . .
. . . . . . . . . 56

4.4 Description textuelle -- Consulter les ´equipes d'un projet . . . .
. . . . . . . . 57

5.1 Backlog de sprint3- Syst` eme de visualisation des donn´ees pour
membres et projets 68

5.2 Description textuelle --Suivi de pr´esence des membres . . . . . . .
. . . . . . . 73

6.1 Backlog du Sprint 4 - Yejoura : Fonctionnalit´es avanc´ees de
gestion de projet . 85

6.3 Cas d'utilisation -- G´erer membres . . . . . . . . . . . . . . . .
. . . . . . . . . 90
\end{quote}

x

\begin{quote}
\textbf{Introduction G´en´erale}
\end{quote}

Dans un contexte ´economique et technologique en constante mutation, les
entreprises sont

appel´ees ` a renforcer leur agilit´e et leur capacit´e de pilotage
strat´egique. Le succ` es d'un projet

repose aujourd'hui non seulement sur sa bonne ex´ecution technique, mais
aussi sur sa gou-

vernance, sa tra¸cabilit´e financi` ere et sa capacit´e ` a s'adapter
rapidement aux changements.

Malheureusement, dans de nombreuses structures, les outils de gestion de
projets sont sou-

vent fragment´es, peu int´egr´es, voire obsol` etes, limitant ainsi la
visibilit´e globale et la prise de

d´ecision ´eclair´ee.

Face ` a ces enjeux, le projet \textbf{Pro-Track} a ´et´e con¸cu comme
une solution num´erique innovante

permettant ` a la fois le \textbf{suivi op´erationnel des projets} et la
\textbf{gestion strat´egique des}

\textbf{ressources financi` eres}. Il s'appuie sur deux plateformes
compl´ementaires :

\begin{quote}
--- \textbf{Pro-Tracker}, orient´ee vers le contrˆole global, la
visualisation des indicateurs de perfor-

mance et la gestion budg´etaire.

--- \textbf{Yejoura}, centr´ee sur la planification d´etaill´ee, la
gestion des tˆaches et la gestion des

jalons, notamment via des outils de type backlog et diagramme de Gantt.
\end{quote}

Ce double dispositif permet une couverture compl` ete du cycle de vie
des projets, du lancement

a la clˆoture, en favorisant la collaboration, la transparence des
donn´ees et la prise de d´ecision

proactive. Afin de garantir la flexibilit´e, l'adaptabilit´e et
l'implication continue des parties

prenantes, le d´eveloppement de la solution a ´et´e men´e selon une
\textbf{m´ethodologie agile}, plus

pr´ecis´ement le cadre \textbf{Scrum}, reposant sur des it´erations
courtes et pilot´ees par les besoins

m´etiers.

Le pr´esent rapport est structur´e de mani` ere ` a refl´eter cette
approche progressive :

\begin{quote}
--- Le \textbf{chapitre 1} pr´esente le cadre g´en´eral du projet, y
compris une \emph{´etude critique de}

\emph{l'existant} et la \emph{m´ethodologie de travail adopt´ee}.

--- Le \textbf{chapitre 2} est d´edi´e ` a l'\emph{analyse et ` a la
conception} de la solution propos´ee, avec une

description des \emph{sp´ecifications des besoins}, ainsi que des
\emph{diff´erents diagrammes d'archi-}

\emph{tecture} .
\end{quote}

Les chapitres suivants retracent l'avancement progressif des diff´erents
\textbf{sprints} r´ealis´es.

\begin{quote}
--- Le \textbf{chapitre 3} couvre le \textbf{Sprint} \textbf{1},
consacr´e ` a l'impl´ementation des modules

d'\emph{authentification}, de \emph{gestion des profils} et des
\emph{param` etres administratifs} dans la pla-

teforme Pro-Tracker.
\end{quote}

1

\begin{quote}
--- Le \textbf{chapitre 4} d´ecrit le \textbf{Sprint 2}, portant sur la
\emph{consultation des projets et des tˆaches},

le \emph{pilotage financier}, la consultation des feuilles de temps et
la gestion des op´erations

dans la plateforme Pro-Tracker.

--- Le \textbf{chapitre 5} couvre le \textbf{Sprint 3}, d´edi´e ` a la
consultation des \emph{KPIs}, ` a la gestion fi-

nanci` ere des projets, ` a la gestion des membres,tableau de bord
global,chatbot.

--- Le \textbf{chapitre} \textbf{6} aborde la plateforme
\textbf{Yejoura}, avec le \textbf{Sprint} \textbf{4} consacr´e `

l'\emph{authentification}, la \emph{gestion des projets}, des
\emph{membres} et des \emph{feuilles de temps}.
\end{quote}

2

\begin{longtable}[]{@{}
  >{\raggedright\arraybackslash}p{(\columnwidth - 2\tabcolsep) * \real{0.5000}}
  >{\raggedright\arraybackslash}p{(\columnwidth - 2\tabcolsep) * \real{0.5000}}@{}}
\toprule()
\begin{minipage}[b]{\linewidth}\raggedright
Chapitre\textbf{1}
\end{minipage} & \begin{minipage}[b]{\linewidth}\raggedright
\begin{quote}
\textbf{Etude pr´ealable}
\end{quote}
\end{minipage} \\
\midrule()
\endhead
\bottomrule()
\end{longtable}

3

\begin{longtable}[]{@{}
  >{\raggedright\arraybackslash}p{(\columnwidth - 2\tabcolsep) * \real{0.5000}}
  >{\raggedright\arraybackslash}p{(\columnwidth - 2\tabcolsep) * \real{0.5000}}@{}}
\toprule()
\multicolumn{2}{@{}>{\raggedright\arraybackslash}p{(\columnwidth - 2\tabcolsep) * \real{1.0000} + 2\tabcolsep}@{}}{%
\begin{minipage}[b]{\linewidth}\raggedright
\begin{quote}
Etude pr´ealable
\end{quote}
\end{minipage}} \\
\midrule()
\endhead
\begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
\textbf{1.1}
\end{quote}
\end{minipage} & \begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
\textbf{Introduction}
\end{quote}
\end{minipage} \\
\multicolumn{2}{@{}>{\raggedright\arraybackslash}p{(\columnwidth - 2\tabcolsep) * \real{1.0000} + 2\tabcolsep}@{}}{%
\begin{minipage}[t]{\linewidth}\raggedright
Dans un environnement num´erique en constante ´evolution, les
entreprises doivent s'adapter

rapidement aux nouvelles technologies pour optimiser leurs processus et
am´eliorer leur effica-

\begin{quote}
cit´e.
\end{quote}

C'est dans ce contexte que s'inscrit le pr´esent projet de stage,
sp´ecialis´ee dans le d´eveloppement

de solutions num´eriques. L'objectif principal de ce travail est de
concevoir et de d´evelopper

deux applications compl´ementaires de gestion et de suivi des projets et
de leur gestion fi-

\begin{quote}
nanci` ere.
\end{quote}

Ce chapitre a pour vocation de pr´esenter le contexte g´en´eral du
projet, l'organisme d'accueil,

les missions confi´ees, la probl´ematique identifi´ee ainsi que la
m´ethodologie adopt´ee pour la

r´ealisation du projet. Il servira de socle ` a la compr´ehension des
choix techniques et organisa-

\begin{quote}
tionnels d´evelopp´es dans les chapitres suivants.
\end{quote}
\end{minipage}} \\
\begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
\textbf{1.2}
\end{quote}
\end{minipage} & \begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
\textbf{Contexte du projet}
\end{quote}
\end{minipage} \\
\multicolumn{2}{@{}>{\raggedright\arraybackslash}p{(\columnwidth - 2\tabcolsep) * \real{1.0000} + 2\tabcolsep}@{}}{%
\begin{minipage}[t]{\linewidth}\raggedright
Notre projet, intitul´e \textbf{''Pro-Tracker''} et
\textbf{''Yejoura''}, s'inscrit dans le cadre de notre stage

de fin d'´etudes au sein de l'entreprise ZETABOX. Il vise ` a
d´evelopper deux plateformes web

\begin{quote}
compl´ementaires :
\end{quote}

\textbf{Yejoura} : une plateforme destin´ee ` a la gestion et au suivi
des projets, permettant une plani-

\begin{quote}
fication efficace, une gestion des tˆaches et une collaboration fluide
entre les ´equipes.
\end{quote}

\textbf{Pro-Tracker} : une plateforme interactive con¸cue pour
am´eliorer le suivi des projets et la

gestion financi` ere. Elle offre aux cadres sup´erieurs une vue
d'ensemble claire, actualis´ee et

fiable de l'´etat d'avancement des projets ainsi que de leur impact
´economique. En assurant

la coh´erence et l'int´egrit´e des donn´ees, Pro-Tracker facilite la
prise de d´ecisions strat´egiques

\begin{quote}
eclair´ees.
\end{quote}

Ces deux plateformes ont pour objectif commun de faciliter la gestion
op´erationnelle et la prise

de d´ecision au sein de l'entreprise, tout en garantissant s´ecurit´e,
performance et accessibilit´e.
\end{minipage}} \\
\bottomrule()
\end{longtable}

4

\begin{longtable}[]{@{}
  >{\raggedright\arraybackslash}p{(\columnwidth - 2\tabcolsep) * \real{0.5000}}
  >{\raggedright\arraybackslash}p{(\columnwidth - 2\tabcolsep) * \real{0.5000}}@{}}
\toprule()
\multicolumn{2}{@{}>{\raggedright\arraybackslash}p{(\columnwidth - 2\tabcolsep) * \real{1.0000} + 2\tabcolsep}@{}}{%
\begin{minipage}[b]{\linewidth}\raggedright
\begin{quote}
Etude pr´ealable
\end{quote}
\end{minipage}} \\
\midrule()
\endhead
\begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
\textbf{1.3}
\end{quote}
\end{minipage} & \begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
\textbf{Organisme d'accueil}
\end{quote}
\end{minipage} \\
\multicolumn{2}{@{}>{\raggedright\arraybackslash}p{(\columnwidth - 2\tabcolsep) * \real{1.0000} + 2\tabcolsep}@{}}{%
\begin{minipage}[t]{\linewidth}\raggedright
Cette section est consacr´ee ` a la pr´esentation de l'entreprise
ZETABOX, ainsi qu'\,` a la descrip-

\begin{quote}
tion de ses principaux secteurs d'activit´es.
\end{quote}
\end{minipage}} \\
\begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
\textbf{1.3.1}
\end{quote}
\end{minipage} & \begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
\textbf{Pr´esentation G´en´erale de l'entreprise ZETABOX}
\end{quote}
\end{minipage} \\
\multicolumn{2}{@{}>{\raggedright\arraybackslash}p{(\columnwidth - 2\tabcolsep) * \real{1.0000} + 2\tabcolsep}@{}}{%
\begin{minipage}[t]{\linewidth}\raggedright
ZETABOX est une soci´et´e de conseil en logiciels bas´ee en Tunisie,
proposant des services de

sous-traitance de projets dans les domaines du d´eveloppement de
logiciels, de l'int´egration

et de l'assurance qualit´e. Avec un accent sur le d´eveloppement de
logiciels, ZETABOX

propose ´egalement des services sp´ecialis´es dans le d´eveloppement
d'applications mobiles, le

\begin{quote}
d´eveloppement d'applications Web, les tests de logiciels et le design
UX/UI.
\end{quote}
\end{minipage}} \\
\bottomrule()
\end{longtable}

\includegraphics[width=3.30556in,height=3.30556in]{vertopal_f50a6211e6524672a7ad79ef0154e85e/media/image1.png}

Figure 1.1 \emph{-- Logo ZetaBox}

5

\begin{longtable}[]{@{}
  >{\raggedright\arraybackslash}p{(\columnwidth - 2\tabcolsep) * \real{0.5000}}
  >{\raggedright\arraybackslash}p{(\columnwidth - 2\tabcolsep) * \real{0.5000}}@{}}
\toprule()
\multicolumn{2}{@{}>{\raggedright\arraybackslash}p{(\columnwidth - 2\tabcolsep) * \real{1.0000} + 2\tabcolsep}@{}}{%
\begin{minipage}[b]{\linewidth}\raggedright
\begin{quote}
Etude pr´ealable
\end{quote}
\end{minipage}} \\
\midrule()
\endhead
\begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
\textbf{1.3.2}
\end{quote}
\end{minipage} & \begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
\textbf{Fiche Signal´etique}
\end{quote}
\end{minipage} \\
\multicolumn{2}{@{}>{\raggedright\arraybackslash}p{(\columnwidth - 2\tabcolsep) * \real{1.0000} + 2\tabcolsep}@{}}{%
\begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
La fiche signal´etique de ZETABOX est la suivante :
\end{quote}

Table 1.1 \emph{-- Informations g´en´erales sur l'entreprise ZETABOX}
\end{minipage}} \\
\bottomrule()
\end{longtable}

\begin{longtable}[]{@{}
  >{\raggedright\arraybackslash}p{(\columnwidth - 2\tabcolsep) * \real{0.5000}}
  >{\raggedright\arraybackslash}p{(\columnwidth - 2\tabcolsep) * \real{0.5000}}@{}}
\toprule()
\begin{minipage}[b]{\linewidth}\raggedright
\textbf{Nom ou Raison sociale}
\end{minipage} & \begin{minipage}[b]{\linewidth}\raggedright
\begin{quote}
ZETABOX
\end{quote}
\end{minipage} \\
\midrule()
\endhead
\begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
\textbf{Site web}
\end{quote}
\end{minipage} & \begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
https ://zeta-box.com
\end{quote}
\end{minipage} \\
\begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
\textbf{Email}
\end{quote}
\end{minipage} & \begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
<EMAIL>@zeta-box.com
\end{quote}
\end{minipage} \\
\begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
\textbf{Adresse}
\end{quote}
\end{minipage} & Av Hedi Nouira, Emna City B-51, Sfax Ejadida, 3027
Sfax, Tunisie \\
\begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
\textbf{Ann´ee de fondation}
\end{quote}
\end{minipage} & \begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
2018
\end{quote}
\end{minipage} \\
\begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
\textbf{T´el´ephone}
\end{quote}
\end{minipage} & \begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
Tunisia : (+216) 22 009 662
\end{quote}
\end{minipage} \\
\bottomrule()
\end{longtable}

\begin{longtable}[]{@{}
  >{\raggedright\arraybackslash}p{(\columnwidth - 2\tabcolsep) * \real{0.5000}}
  >{\raggedright\arraybackslash}p{(\columnwidth - 2\tabcolsep) * \real{0.5000}}@{}}
\toprule()
\begin{minipage}[b]{\linewidth}\raggedright
\begin{quote}
\textbf{1.3.3}
\end{quote}
\end{minipage} & \begin{minipage}[b]{\linewidth}\raggedright
\begin{quote}
\textbf{Diff´erents services}
\end{quote}
\end{minipage} \\
\midrule()
\endhead
\bottomrule()
\end{longtable}

\begin{quote}
ZETABOX offre une large gamme de services, y compris :

- D´eveloppement de Produits Logiciels : ZETABOX offre des services de
d´eveloppement logiciel

complet, depuis le concept initial jusqu'\,` a la livraison et au-del`
a, en incluant la documenta-

tion des besoins, le design, le d´eveloppement, les tests, le
d´eploiement et la maintenance. Ses

experts techniques transforment rapidement les id´ees en produits
num´eriques avec un budget

minimum, en utilisant des frameworks, outils et technologies ` a la
pointe de la technologie.

ZETABOX suit la m´ethodologie agile Scrum pour obtenir des r´esultats
rapides et garantir

une am´elioration continue.

- Design UX/UI : L'´equipe de designers exp´eriment´es de ZetaBox cr´ee
des interfaces intuitives

et centr´ees sur l'utilisateur pour les produits logiciels. Ils
commencent par comprendre le but

de l'application web ou mobile, puis ´elaborent un design tendance en
collaboration avec des

chercheurs qualifi´es. ZetaBox r´ealise des wireframes et prototypes
pour offrir une pr´esentation

visuelle du produit num´erique, avec des it´erations de design jusqu'\,`
a ce qu'un prototype inter-

actif soit prˆet ` a ˆetre test´e.

- Consultants IT ` a la Demande : ZETABOX renforce les ´equipes de ses
clients avec des pro-

fessionnels IT qualifi´es ` a un tarif journalier. Le responsable du
d´eveloppement commercial

de ZETABOX aide ` a s´electionner les bons profils, tels que
gestionnaires de projet, designers,

d´eveloppeurs, testeurs et int´egrateurs. Une fois les profils choisis,
les clients peuvent g´erer

leur ´equipe qualifi´ee pour acc´el´erer le d´eveloppement de leurs
logiciels tout en respectant leur

budget.
\end{quote}

6

\begin{longtable}[]{@{}
  >{\raggedright\arraybackslash}p{(\columnwidth - 2\tabcolsep) * \real{0.5000}}
  >{\raggedright\arraybackslash}p{(\columnwidth - 2\tabcolsep) * \real{0.5000}}@{}}
\toprule()
\multicolumn{2}{@{}>{\raggedright\arraybackslash}p{(\columnwidth - 2\tabcolsep) * \real{1.0000} + 2\tabcolsep}@{}}{%
\begin{minipage}[b]{\linewidth}\raggedright
\begin{quote}
Etude pr´ealable
\end{quote}
\end{minipage}} \\
\midrule()
\endhead
\begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
\textbf{1.3.4}
\end{quote}
\end{minipage} & \begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
\textbf{Organigramme de l'entreprise ZETABOX}
\end{quote}
\end{minipage} \\
\multicolumn{2}{@{}>{\raggedright\arraybackslash}p{(\columnwidth - 2\tabcolsep) * \real{1.0000} + 2\tabcolsep}@{}}{%
\begin{minipage}[t]{\linewidth}\raggedright
La structure organisationnelle de l'entreprise ZETABOX est repr´esent´ee
dans la figure 1.2.

Cette structure s'appuie sur une strat´egie r´egionale qui vise ` a
fournir un soutien local ` a ses

clients internationaux dans leur transformation num´erique. ZETABOX est
une entreprise mon-

\begin{quote}
diale, parfaitement int´egr´ee et prˆete ` a se d´evelopper.
\end{quote}
\end{minipage}} \\
\bottomrule()
\end{longtable}

\includegraphics[width=6.61111in,height=3.87083in]{vertopal_f50a6211e6524672a7ad79ef0154e85e/media/image2.png}

Figure 1.2 \emph{-- Organigramme de l'entreprise ZETABOX}

\begin{longtable}[]{@{}
  >{\raggedright\arraybackslash}p{(\columnwidth - 0\tabcolsep) * \real{1.0000}}@{}}
\toprule()
\begin{minipage}[b]{\linewidth}\raggedright
J'ai r´ealis´e mon projet au sein du d´epartement de d´eveloppement
logiciel de l'entreprise ZE-

TABOX, o` u j'ai align´e mes objectifs avec ceux de l'´equipe afin de
concevoir et d´evelopper deux

plateformes compl´ementaires : Yejoura, d´edi´ee ` a la gestion et ` a
la planification des projets,

et Pro-Tracker, destin´ee au suivi de l'avancement des projets ainsi
qu'au suivi financier. Ces

solutions visent ` a am´eliorer la coordination, la tra¸cabilit´e et la
performance globale des projets

\begin{quote}
men´es au sein de l'entreprise.
\end{quote}
\end{minipage} \\
\midrule()
\endhead
\bottomrule()
\end{longtable}

7

\begin{longtable}[]{@{}
  >{\raggedright\arraybackslash}p{(\columnwidth - 2\tabcolsep) * \real{0.5000}}
  >{\raggedright\arraybackslash}p{(\columnwidth - 2\tabcolsep) * \real{0.5000}}@{}}
\toprule()
\multicolumn{2}{@{}>{\raggedright\arraybackslash}p{(\columnwidth - 2\tabcolsep) * \real{1.0000} + 2\tabcolsep}@{}}{%
\begin{minipage}[b]{\linewidth}\raggedright
\begin{quote}
Etude pr´ealable
\end{quote}
\end{minipage}} \\
\midrule()
\endhead
\begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
\textbf{1.4}
\end{quote}
\end{minipage} & \begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
\textbf{D´efinition de la mission}
\end{quote}
\end{minipage} \\
\begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
\textbf{1.4.1}
\end{quote}
\end{minipage} & \begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
\textbf{Cadre de travail}
\end{quote}
\end{minipage} \\
\multicolumn{2}{@{}>{\raggedright\arraybackslash}p{(\columnwidth - 2\tabcolsep) * \real{1.0000} + 2\tabcolsep}@{}}{%
\begin{minipage}[t]{\linewidth}\raggedright
Ce stage s'inscrit dans le cadre d'un projet de fin d'´etude, visant ` a
acqu´erir une exp´erience

pratique enrichissante et ` a obtenir une attestation de l'institut
international de technologie de

Sfax. Nous avons r´ealis´e notre stage au sein de la soci´et´e ZetaBox,
sous l'encadrement de M.

\begin{quote}
Hassen knani , pendant une p´eriode de quatre mois.
\end{quote}
\end{minipage}} \\
\begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
\textbf{1.4.2}
\end{quote}
\end{minipage} & \begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
\textbf{Pr´esentation du projet}
\end{quote}
\end{minipage} \\
\multicolumn{2}{@{}>{\raggedright\arraybackslash}p{(\columnwidth - 2\tabcolsep) * \real{1.0000} + 2\tabcolsep}@{}}{%
\begin{minipage}[t]{\linewidth}\raggedright
Sous l'impulsion de M. Mohamed Elfakhfekh, CEO de ZetaBox, nous
d´eveloppons une solution

r´evolutionnaire unifiant la gestion op´erationnelle et le pilotage
financier des projets. Yejoura

offre des fonctionnalit´es de gestion des projets, tandis que
Pro-Tracker se concentre sur le suivi

\begin{quote}
des projets et l'analyse financi` ere.
\end{quote}

Cette plateforme int´egr´ee offre aux ´equipes une collaboration fluide
et aux d´ecideurs une

visibilit´e en temps r´eel sur les indicateurs cl´es. En combinant
agilit´e et maˆıtrise financi` ere,

notre solution accompagne les entreprises dans l'optimisation et la
r´eussite durable de leurs

\begin{quote}
projets, mˆeme dans des environnements complexes et changeants.
\end{quote}
\end{minipage}} \\
\begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
\textbf{1.4.3}
\end{quote}
\end{minipage} & \begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
\textbf{Objectifs ` a atteindre}
\end{quote}
\end{minipage} \\
\multicolumn{2}{@{}>{\raggedright\arraybackslash}p{(\columnwidth - 2\tabcolsep) * \real{1.0000} + 2\tabcolsep}@{}}{%
\begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
Nous visons ` a atteindre les objectifs suivants :

--- \textbf{Plateforme centralis´ee} : Regrouper toutes les donn´ees
projets et financi` eres dans un

syst` eme unique pour assurer un suivi global et structur´e.
\end{quote}
\end{minipage}} \\
\bottomrule()
\end{longtable}

\begin{quote}
--- \textbf{Gestion et suivi clair des projets} : Assurer une gestion
efficace des projets et un

suivi structur´e de leur avancement.

--- \textbf{Gestion financi` ere pr´ecise} : Assurer le suivi des
budgets, des d´epenses et des

pr´evisions pour l'ensemble de la soci´et´e, dans plusieurs devises,
avec une analyse continue

des ´ecarts.

--- \textbf{S´ecurit´e et conformit´e} : Garantir l'int´egrit´e, la
confidentialit´e et la conformit´e des

donn´ees financi` eres via des m´ecanismes s´ecuris´es.

--- \textbf{Collaboration fluide} : Faciliter les ´echanges et la
coordination entre les ´equipes `

travers une interface intuitive et collaborative.
\end{quote}

8

\begin{longtable}[]{@{}
  >{\raggedright\arraybackslash}p{(\columnwidth - 2\tabcolsep) * \real{0.5000}}
  >{\raggedright\arraybackslash}p{(\columnwidth - 2\tabcolsep) * \real{0.5000}}@{}}
\toprule()
\multicolumn{2}{@{}>{\raggedright\arraybackslash}p{(\columnwidth - 2\tabcolsep) * \real{1.0000} + 2\tabcolsep}@{}}{%
\begin{minipage}[b]{\linewidth}\raggedright
\begin{quote}
Etude pr´ealable
\end{quote}
\end{minipage}} \\
\midrule()
\endhead
\begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
\textbf{1.5}
\end{quote}
\end{minipage} & \begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
\textbf{Probl´ematique}
\end{quote}
\end{minipage} \\
\multicolumn{2}{@{}>{\raggedright\arraybackslash}p{(\columnwidth - 2\tabcolsep) * \real{1.0000} + 2\tabcolsep}@{}}{%
\begin{minipage}[t]{\linewidth}\raggedright
Dans un environnement ´economique marqu´e par la complexit´e croissante
des projets et

l'imp´eratif de maˆıtrise budg´etaire, les organisations peinent ` a
concilier efficacit´e op´erationnelle

et rigueur financi` ere. Les solutions existantes, souvent fragment´ees
entre outils de gestion de

tˆaches et syst` emes financiers, cr´eent des silos d'information qui
nuisent ` a la visibilit´e glo-

bale et ` a la r´eactivit´e d´ecisionnelle. Ce projet vise ` a r´esoudre
cette tension fondamentale

en d´eveloppant une plateforme int´egr´ee capable de corr´eler
dynamiquement l'ex´ecution des

\begin{quote}
projets avec leur performance financi` ere.
\end{quote}

La v´eritable innovation r´eside dans la cr´eation d'un ´ecosyst` eme
unifi´e o` u chaque action

op´erationnelle (avancement des tˆaches...) se refl` ete instantan´ement
dans les indicateurs finan-

ciers, et inversement. Le d´efi technique majeur consiste ` a concevoir
une architecture suffisam-

ment robuste pour garantir la coh´erence des donn´ees entre modules,
tout en restant suffisam-

ment flexible pour s'adapter aux sp´ecificit´es m´etiers de diff´erents
secteurs. Cette probl´ematique

interroge in fine la capacit´e des outils digitaux ` a transformer la
gestion de projets en v´eritable

\begin{quote}
levier de performance ´economique.
\end{quote}
\end{minipage}} \\
\begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
\textbf{1.6}
\end{quote}
\end{minipage} & \begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
\textbf{Etude de l'existant´}
\end{quote}
\end{minipage} \\
\begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
\textbf{1.6.1}
\end{quote}
\end{minipage} & \begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
\textbf{Outils actuellement utilis´es}
\end{quote}
\end{minipage} \\
\bottomrule()
\end{longtable}

Dans l'environnement actuel, la gestion et le suivi des projets reposent
sur l'utilisation d'outils

\begin{quote}
h´et´erog` enes et peu int´egr´es. Ces outils comprennent :

--- Des fichiers Excel, utilis´es pour le suivi des budgets, des
´ech´eances et des indicateurs

cl´es.

--- Des logiciels de gestion de tˆaches ou de planification tels que
\emph{Trello}, permettant de suivre

l'avancement des activit´es.

--- Des logiciels comptables utilis´es pour le traitement financier,
souvent ind´ependants des

outils de gestion de projet.
\end{quote}

\begin{longtable}[]{@{}
  >{\raggedright\arraybackslash}p{(\columnwidth - 0\tabcolsep) * \real{1.0000}}@{}}
\toprule()
\begin{minipage}[b]{\linewidth}\raggedright
\begin{quote}
Ces outils pr´esentent plusieurs limites, r´esum´ees dans le table
suivant :
\end{quote}
\end{minipage} \\
\midrule()
\endhead
\bottomrule()
\end{longtable}

9

\begin{longtable}[]{@{}
  >{\raggedright\arraybackslash}p{(\columnwidth - 0\tabcolsep) * \real{1.0000}}@{}}
\toprule()
\begin{minipage}[b]{\linewidth}\raggedright
\begin{quote}
Etude pr´ealable
\end{quote}
\end{minipage} \\
\midrule()
\endhead
Table 1.2 \emph{-- R´esum´e des outils actuels et de leurs limites} \\
\bottomrule()
\end{longtable}

\begin{longtable}[]{@{}
  >{\raggedright\arraybackslash}p{(\columnwidth - 4\tabcolsep) * \real{0.3333}}
  >{\raggedright\arraybackslash}p{(\columnwidth - 4\tabcolsep) * \real{0.3333}}
  >{\raggedright\arraybackslash}p{(\columnwidth - 4\tabcolsep) * \real{0.3333}}@{}}
\toprule()
\begin{minipage}[b]{\linewidth}\raggedright
\begin{quote}
\textbf{Outil}
\end{quote}
\end{minipage} & \begin{minipage}[b]{\linewidth}\raggedright
\begin{quote}
\textbf{Utilisation principale}
\end{quote}
\end{minipage} & \begin{minipage}[b]{\linewidth}\raggedright
\begin{quote}
\textbf{Points faibles}
\end{quote}
\end{minipage} \\
\midrule()
\endhead
\begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
Fichiers Excel
\end{quote}
\end{minipage} & \begin{minipage}[t]{\linewidth}\raggedright
Suivi budg´etaire, ´ech´eances, indi-

\begin{quote}
cateurs
\end{quote}
\end{minipage} & \begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
- Faible collaboration\\
- Risque d'erreurs manuelles- Absence de mises ` a jour en temps r´eel
\end{quote}\strut
\end{minipage} \\
Trello (ou ´equivalent) & \begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
Gestion des tˆaches, planification
\end{quote}
\end{minipage} & \begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
- Non int´egr´e aux outils fi-nanciers\\
- Visibilit´e limit´ee sur l'en-semble du projet\\
- Gestion documentaire peu structur´ee
\end{quote}\strut
\end{minipage} \\
\begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
Logiciels comptables
\end{quote}
\end{minipage} & \begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
Traitement financier, gestion des paiements
\end{quote}
\end{minipage} & \begin{minipage}[t]{\linewidth}\raggedright
- Isolation par rapport ` a la

\begin{quote}
planification
\end{quote}

\begin{longtable}[]{@{}
  >{\raggedright\arraybackslash}p{(\columnwidth - 6\tabcolsep) * \real{0.2500}}
  >{\raggedright\arraybackslash}p{(\columnwidth - 6\tabcolsep) * \real{0.2500}}
  >{\raggedright\arraybackslash}p{(\columnwidth - 6\tabcolsep) * \real{0.2500}}
  >{\raggedright\arraybackslash}p{(\columnwidth - 6\tabcolsep) * \real{0.2500}}@{}}
\toprule()
\begin{minipage}[b]{\linewidth}\raggedright
-
\end{minipage} & \begin{minipage}[b]{\linewidth}\raggedright
Donn´ees
\end{minipage} & \begin{minipage}[b]{\linewidth}\raggedright
non
\end{minipage} & \begin{minipage}[b]{\linewidth}\raggedright
\begin{quote}
synchro-
\end{quote}
\end{minipage} \\
\midrule()
\endhead
\multicolumn{2}{@{}>{\raggedright\arraybackslash}p{(\columnwidth - 6\tabcolsep) * \real{0.5000} + 2\tabcolsep}}{%
\begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
nis´ees
\end{quote}
\end{minipage}} & \multirow{2}{*}{de} & \multirow{2}{*}{reporting} \\
- & Difficult´ \\
\bottomrule()
\end{longtable}

\begin{quote}
consolid´
\end{quote}
\end{minipage} \\
\bottomrule()
\end{longtable}

\begin{longtable}[]{@{}
  >{\raggedright\arraybackslash}p{(\columnwidth - 2\tabcolsep) * \real{0.5000}}
  >{\raggedright\arraybackslash}p{(\columnwidth - 2\tabcolsep) * \real{0.5000}}@{}}
\toprule()
\begin{minipage}[b]{\linewidth}\raggedright
\begin{quote}
\textbf{1.6.2}
\end{quote}
\end{minipage} & \begin{minipage}[b]{\linewidth}\raggedright
\begin{quote}
\textbf{Limites et probl´ematiques identifi´ees}
\end{quote}
\end{minipage} \\
\midrule()
\endhead
\multicolumn{2}{@{}>{\raggedright\arraybackslash}p{(\columnwidth - 2\tabcolsep) * \real{1.0000} + 2\tabcolsep}@{}}{%
\begin{minipage}[t]{\linewidth}\raggedright
Manque de communication entre les outils : les informations sont
dispers´ees, ce qui complique

\begin{quote}
la consolidation des donn´ees.
\end{quote}

Risque d'erreurs et de doublons : en raison de saisies manuelles ou de
transferts de donn´ees

\begin{quote}
entre outils non connect´es.
\end{quote}

Suivi financier limit´e : absence de visibilit´e en temps r´eel sur les
´ecarts entre budget pr´evisionnel

\begin{quote}
et d´epenses r´eelles.
\end{quote}

Manque de visibilit´e pour la direction : difficile d'obtenir une vue
synth´etique sur l'´etat
\end{minipage}} \\
\bottomrule()
\end{longtable}

\begin{quote}
d'avancement des projets et leur performance.
\end{quote}

• \textbf{Description de l'existant :} Actuellement, la gestion des
projets au sein de l'entreprise

\begin{longtable}[]{@{}
  >{\raggedright\arraybackslash}p{(\columnwidth - 0\tabcolsep) * \real{1.0000}}@{}}
\toprule()
\begin{minipage}[b]{\linewidth}\raggedright
repose sur une diversit´e d'outils non centralis´es, tels que des
fichiers Excel pour le suivi des

tˆaches et des budgets, ainsi que des logiciels de gestion de tˆaches
utilis´es de mani` ere isol´ee
\end{minipage} \\
\midrule()
\endhead
\bottomrule()
\end{longtable}

10

\begin{longtable}[]{@{}
  >{\raggedright\arraybackslash}p{(\columnwidth - 0\tabcolsep) * \real{1.0000}}@{}}
\toprule()
\begin{minipage}[b]{\linewidth}\raggedright
\begin{quote}
Etude pr´ealable
\end{quote}
\end{minipage} \\
\midrule()
\endhead
\begin{minipage}[t]{\linewidth}\raggedright
par certains services. Le suivi financier est g´er´e s´epar´ement par le
service comptable, sans

\begin{quote}
int´egration avec les donn´ees de gestion de projet.
\end{quote}

Cette organisation fragment´ee engendre un manque de visibilit´e
globale, une coordination

difficile entre les ´equipes, des risques d'erreurs dans les donn´ees,
et une perte de temps li´ee `

la collecte manuelle des informations. En l'absence d'un syst` eme
unifi´e, la direction ne dispose

pas d'une vue consolid´ee sur l'´etat d'avancement des projets ni sur
leur impact financier,

\begin{quote}
limitant ainsi la prise de d´ecision strat´egique.
\end{quote}

• \textbf{Critique de l'existant :} La structure actuelle de gestion des
projets pr´esente plusieurs

insuffisances majeures qui freinent l'efficacit´e globale de
l'organisation. L'absence d'un

syst` eme centralis´e entraˆıne une dispersion de l'information, rendant
difficile la consolidation

des donn´ees et le suivi en temps r´eel. Les outils utilis´es ne sont
pas interconnect´es, ce qui

engendre des doublons, des erreurs de saisie et une perte de temps
consid´erable pour les
\end{minipage} \\
\bottomrule()
\end{longtable}

\begin{quote}
equipes.
\end{quote}

Le suivi financier, g´er´e ind´ependamment du pilotage op´erationnel des
projets, limite fortement

la capacit´e ` a anticiper les d´erives budg´etaires. De plus, la
communication entre services est

fragment´ee, nuisant ` a la coordination et ` a la r´eactivit´e. Enfin,
le manque de visibilit´e et

de tableaux de bord d´ecisionnels prive les cadres dirigeants d'une
vision strat´egique claire,

compromettant la prise de d´ecisions ´eclair´ees et le pilotage efficace
de la performance des

\begin{quote}
projets.
\end{quote}

\begin{longtable}[]{@{}
  >{\raggedright\arraybackslash}p{(\columnwidth - 2\tabcolsep) * \real{0.5000}}
  >{\raggedright\arraybackslash}p{(\columnwidth - 2\tabcolsep) * \real{0.5000}}@{}}
\toprule()
\begin{minipage}[b]{\linewidth}\raggedright
\begin{quote}
\textbf{1.7}
\end{quote}
\end{minipage} & \begin{minipage}[b]{\linewidth}\raggedright
\begin{quote}
\textbf{Solution propos´ee}
\end{quote}
\end{minipage} \\
\midrule()
\endhead
\multicolumn{2}{@{}>{\raggedright\arraybackslash}p{(\columnwidth - 2\tabcolsep) * \real{1.0000} + 2\tabcolsep}@{}}{%
\begin{minipage}[t]{\linewidth}\raggedright
Afin de r´epondre aux limites identifi´ees dans l'organisation actuelle,
il est propos´e de mettre

\begin{quote}
en place deux plateformes compl´ementaires et int´egr´ees : Yejoura et
Pro-Tracker.
\end{quote}

Yejoura sera d´edi´e ` a la gestion op´erationnelle des projets :
planification, d´efinition des tˆaches,

suivi des ´etapes et collaboration avec les ´equipes. Elle offrira une
interface intuitive pour les

chefs de projet et les ´equipes terrain, avec des fonctionnalit´es de
gestion agile, de notifications.

Pro-Tracker viendra en appui pour assurer le suivi strat´egique et
financier des projets. Cette

plateforme permettra ` a la direction d'avoir une vue consolid´ee et en
temps r´eel sur l'ensemble

des projets en cours : indicateurs cl´es de performance (KPIs), suivi
budg´etaire, analyse des
\end{minipage}} \\
\bottomrule()
\end{longtable}

11

\begin{longtable}[]{@{}
  >{\raggedright\arraybackslash}p{(\columnwidth - 2\tabcolsep) * \real{0.5000}}
  >{\raggedright\arraybackslash}p{(\columnwidth - 2\tabcolsep) * \real{0.5000}}@{}}
\toprule()
\multicolumn{2}{@{}>{\raggedright\arraybackslash}p{(\columnwidth - 2\tabcolsep) * \real{1.0000} + 2\tabcolsep}@{}}{%
\begin{minipage}[b]{\linewidth}\raggedright
\begin{quote}
Etude pr´ealable
\end{quote}
\end{minipage}} \\
\midrule()
\endhead
\multicolumn{2}{@{}>{\raggedright\arraybackslash}p{(\columnwidth - 2\tabcolsep) * \real{1.0000} + 2\tabcolsep}@{}}{%
\begin{minipage}[t]{\linewidth}\raggedright
ecarts, et rapports financiers. Elle garantira ´egalement la
s´ecurisation et la tra¸cabilit´e des

\begin{quote}
donn´ees financi` eres, tout en assurant la conformit´e avec les normes
en vigueur.
\end{quote}

L'int´egration des deux plateformes permettra de centraliser les
informations, d'am´eliorer la

coordination entre les services, de r´eduire les erreurs et de renforcer
la capacit´e de pilotage

strat´egique. Ce dispositif assurera une meilleure r´eactivit´e, une
transparence accrue et une

\begin{quote}
prise de d´ecision plus efficace ` a tous les niveaux de l'organisation.
\end{quote}
\end{minipage}} \\
\begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
\textbf{1.8}
\end{quote}
\end{minipage} & \begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
\textbf{M´ethodologie de travail}
\end{quote}
\end{minipage} \\
\multicolumn{2}{@{}>{\raggedright\arraybackslash}p{(\columnwidth - 2\tabcolsep) * \real{1.0000} + 2\tabcolsep}@{}}{%
\begin{minipage}[t]{\linewidth}\raggedright
Pour garantir le succ` es de notre projet, il est essentiel de mettre en
place une m´ethodologie

claire pour le guider et l'encadrer de mani` ere structur´ee. Nous avons
choisi d'adopter la

m´ethode Scrum, une approche agile de gestion de projets, pour le
d´eveloppement de notre

initiative. Scrum est un cadre de travail l´eger qui aide les individus,
les ´equipes et les organisa-

tions ` a g´en´erer de la valeur en proposant des solutions adaptatives
` a des probl` emes complexes.

\begin{quote}
En bref, Scrum a besoin d'un Scrum Master pour favoriser un
environnement o` u :
\end{quote}

\textbf{Le Product Owner} d´efinit et ordonne les ´el´ements du Product
Backlog afin de r´epondre

\begin{quote}
aux besoins m´etiers et maximiser la valeur produite.
\end{quote}
\end{minipage}} \\
\bottomrule()
\end{longtable}

\textbf{La Scrum Master} facilite le processus Scrum, s'assure que les
bonnes pratiques sont suivies,

\begin{quote}
et aide l'´equipe ` a travailler efficacement sans obstacles.
\end{quote}

\textbf{La Scrum Team} s´electionne les ´el´ements ` a r´ealiser, les
transforme en un increment de valeur

durant le Sprint, et collabore avec les parties prenantes pour inspecter
les r´esultats et s'adapter

\begin{quote}
pour le sprint suivant.
\end{quote}

12

\begin{longtable}[]{@{}
  >{\raggedright\arraybackslash}p{(\columnwidth - 0\tabcolsep) * \real{1.0000}}@{}}
\toprule()
\begin{minipage}[b]{\linewidth}\raggedright
\begin{quote}
Etude pr´ealable
\end{quote}
\end{minipage} \\
\midrule()
\endhead
\bottomrule()
\end{longtable}

\includegraphics[width=5.28889in,height=3.35556in]{vertopal_f50a6211e6524672a7ad79ef0154e85e/media/image3.png}

Figure 1.3 \emph{-- M´ethodologie Scrum}

Le cadre de travail Scrum repose sur l'intelligence collective des
individus qui l'utilisent. Plutˆot

que de fournir des instructions d´etaill´ees, les r` egles de Scrum
orientent les relations et les inter-

actions entre les personnes. Divers processus, techniques et m´ethodes
peuvent ˆetre employ´es

dans ce cadre de travail. Scrum englobe des pratiques existantes ou les
rend inutiles. Il rend

visible l'efficacit´e relative du management, de l'environnement et des
techniques de travail

\begin{quote}
actuels, ce qui permet d'apporter des am´eliorations continues.
\end{quote}

Le cycle de vie d'un projet Scrum suit un enchaˆınement bien sp´ecifi´e,
comme il est illustr´

\begin{quote}
dans la figure 1.4 .
\end{quote}

\includegraphics[width=6.61111in,height=2.43056in]{vertopal_f50a6211e6524672a7ad79ef0154e85e/media/image4.png}

\begin{longtable}[]{@{}
  >{\raggedright\arraybackslash}p{(\columnwidth - 0\tabcolsep) * \real{1.0000}}@{}}
\toprule()
\begin{minipage}[b]{\linewidth}\raggedright
Figure 1.4 \emph{-- Cycle de vie de la m´ethodologie SCRUM}
\end{minipage} \\
\midrule()
\endhead
\bottomrule()
\end{longtable}

13

\begin{longtable}[]{@{}
  >{\raggedright\arraybackslash}p{(\columnwidth - 0\tabcolsep) * \real{1.0000}}@{}}
\toprule()
\begin{minipage}[b]{\linewidth}\raggedright
\begin{quote}
Etude pr´ealable
\end{quote}
\end{minipage} \\
\midrule()
\endhead
\begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
Le table suivant pr´esente l'´equipe Scrum dans notre projet.
\end{quote}

Table 1.3 \emph{-- R´epartition des rˆoles dans l'´equipe Scrum}
\end{minipage} \\
\bottomrule()
\end{longtable}

\begin{longtable}[]{@{}
  >{\raggedright\arraybackslash}p{(\columnwidth - 4\tabcolsep) * \real{0.3333}}
  >{\raggedright\arraybackslash}p{(\columnwidth - 4\tabcolsep) * \real{0.3333}}
  >{\raggedright\arraybackslash}p{(\columnwidth - 4\tabcolsep) * \real{0.3333}}@{}}
\toprule()
\begin{minipage}[b]{\linewidth}\raggedright
\begin{quote}
\textbf{Rˆole}
\end{quote}
\end{minipage} & \begin{minipage}[b]{\linewidth}\raggedright
\begin{quote}
\textbf{Responsabilit´}
\end{quote}
\end{minipage} & \begin{minipage}[b]{\linewidth}\raggedright
\begin{quote}
\textbf{Acteur(s)}
\end{quote}
\end{minipage} \\
\midrule()
\endhead
\begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
Product Owner
\end{quote}
\end{minipage} & \begin{minipage}[t]{\linewidth}\raggedright
D´efinit le produit selon les at-

\begin{quote}
tentes des utilisateurs
\end{quote}
\end{minipage} & \begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
product owner
\end{quote}
\end{minipage} \\
\begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
Scrum Master
\end{quote}
\end{minipage} & \begin{minipage}[t]{\linewidth}\raggedright
\begin{longtable}[]{@{}
  >{\raggedright\arraybackslash}p{(\columnwidth - 6\tabcolsep) * \real{0.2500}}
  >{\raggedright\arraybackslash}p{(\columnwidth - 6\tabcolsep) * \real{0.2500}}
  >{\raggedright\arraybackslash}p{(\columnwidth - 6\tabcolsep) * \real{0.2500}}
  >{\raggedright\arraybackslash}p{(\columnwidth - 6\tabcolsep) * \real{0.2500}}@{}}
\toprule()
\begin{minipage}[b]{\linewidth}\raggedright
Expertise
\end{minipage} & \begin{minipage}[b]{\linewidth}\raggedright
du
\end{minipage} & \begin{minipage}[b]{\linewidth}\raggedright
domaine
\end{minipage} & \begin{minipage}[b]{\linewidth}\raggedright
m´etier,
\end{minipage} \\
\midrule()
\endhead
\bottomrule()
\end{longtable}

maˆıtrise Scrum et assure le bon

\begin{quote}
d´eroulement du projet
\end{quote}
\end{minipage} & \begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
scrum master
\end{quote}
\end{minipage} \\
\begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
Scrum Team
\end{quote}
\end{minipage} & \begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
R´ealisation du produit
\end{quote}
\end{minipage} & \begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
3 D´eveloppeurs
\end{quote}
\end{minipage} \\
\bottomrule()
\end{longtable}

\begin{longtable}[]{@{}
  >{\raggedright\arraybackslash}p{(\columnwidth - 2\tabcolsep) * \real{0.5000}}
  >{\raggedright\arraybackslash}p{(\columnwidth - 2\tabcolsep) * \real{0.5000}}@{}}
\toprule()
\begin{minipage}[b]{\linewidth}\raggedright
\begin{quote}
\textbf{1.8.1}
\end{quote}
\end{minipage} & \begin{minipage}[b]{\linewidth}\raggedright
\begin{quote}
\textbf{Planification d'un projet SCRUM}
\end{quote}
\end{minipage} \\
\midrule()
\endhead
\multicolumn{2}{@{}>{\raggedright\arraybackslash}p{(\columnwidth - 2\tabcolsep) * \real{1.0000} + 2\tabcolsep}@{}}{%
La planification d'un projet Scrum est une m´ethode agile qui vise ` a
livrer rapidement des

fonctionnalit´es de qualit´e tout en restant adaptable aux changements.
Voici les ´etapes cl´es de} \\
\bottomrule()
\end{longtable}

\begin{quote}
ce processus :
\end{quote}

\begin{longtable}[]{@{}
  >{\raggedright\arraybackslash}p{(\columnwidth - 2\tabcolsep) * \real{0.5000}}
  >{\raggedright\arraybackslash}p{(\columnwidth - 2\tabcolsep) * \real{0.5000}}@{}}
\toprule()
\multicolumn{2}{@{}>{\raggedright\arraybackslash}p{(\columnwidth - 2\tabcolsep) * \real{1.0000} + 2\tabcolsep}@{}}{%
\begin{minipage}[b]{\linewidth}\raggedright
\begin{quote}
--- \textbf{D´efinir le product backlog :} Le product backlog est une
liste des fonctionnalit´es `

r´ealiser pour le projet, tri´ee en fonction de la valeur apport´ee par
chaque fonctionnalit´e.

--- Cette ´etape est r´ealis´ee par le Product Owner.
\end{quote}
\end{minipage}} \\
\midrule()
\endhead
--- & \begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
\textbf{Estimer la charge de travail :} Pour chaque ´el´ement du product
backlog, l'´equipe de
\end{quote}
\end{minipage} \\
\multicolumn{2}{@{}>{\raggedright\arraybackslash}p{(\columnwidth - 2\tabcolsep) * \real{1.0000} + 2\tabcolsep}@{}}{%
\begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
d´eveloppement estime le temps n´ecessaire ` a sa r´ealisation.
\end{quote}
\end{minipage}} \\
--- & \begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
\textbf{Planifier le sprint :} Le sprint, d'une dur´ee de 2 ` a 4
semaines, est une p´eriode pendant
\end{quote}
\end{minipage} \\
\multicolumn{2}{@{}>{\raggedright\arraybackslash}p{(\columnwidth - 2\tabcolsep) * \real{1.0000} + 2\tabcolsep}@{}}{%
\begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
laquelle l'´equipe de d´eveloppement travaille sur un ensemble de
fonctionnalit´es issues du

product backlog. L'´equipe s´electionne les fonctionnalit´es ` a
r´ealiser pendant le sprint.
\end{quote}
\end{minipage}} \\
--- & \begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
\textbf{D´efinir le sprint backlog :} Le sprint backlog est la liste des
tˆaches ` a r´ealiser pour
\end{quote}
\end{minipage} \\
\multicolumn{2}{@{}>{\raggedright\arraybackslash}p{(\columnwidth - 2\tabcolsep) * \real{1.0000} + 2\tabcolsep}@{}}{%
\begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
chaque fonctionnalit´e s´electionn´ee pour le sprint. Cette ´etape est
effectu´ee par l'´equipe

de d´eveloppement.

--- \textbf{R´eunion de planification du sprint} : Organis´ee au d´ebut
de chaque sprint, cette

r´eunion permet de d´efinir les objectifs du sprint, de clarifier les
´el´ements du product

backlog s´electionn´es et de finaliser le sprint backlog.
\end{quote}

Une fois la planification termin´ee, l'´equipe de d´eveloppement
travaille sur les fonctionnalit´es du

sprint backlog de mani` ere it´erative et organise des r´eunions
quotidiennes pour suivre l'avan-

cement. ` A la fin du sprint, l'´equipe pr´esente le r´esultat de son
travail au Product Owner

lors d'une r´eunion de revue de sprint, permettant ´egalement de
recueillir des feedbacks pour

\begin{quote}
am´eliorer les prochains sprints.
\end{quote}
\end{minipage}} \\
\bottomrule()
\end{longtable}

14

\begin{longtable}[]{@{}
  >{\raggedright\arraybackslash}p{(\columnwidth - 2\tabcolsep) * \real{0.5000}}
  >{\raggedright\arraybackslash}p{(\columnwidth - 2\tabcolsep) * \real{0.5000}}@{}}
\toprule()
\multicolumn{2}{@{}>{\raggedright\arraybackslash}p{(\columnwidth - 2\tabcolsep) * \real{1.0000} + 2\tabcolsep}@{}}{%
\begin{minipage}[b]{\linewidth}\raggedright
\begin{quote}
Etude pr´ealable
\end{quote}
\end{minipage}} \\
\midrule()
\endhead
\begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
\textbf{1.8.2}
\end{quote}
\end{minipage} & \begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
\textbf{Outil de planification du travail}
\end{quote}
\end{minipage} \\
\multicolumn{2}{@{}>{\raggedright\arraybackslash}p{(\columnwidth - 2\tabcolsep) * \real{1.0000} + 2\tabcolsep}@{}}{%
\begin{minipage}[t]{\linewidth}\raggedright
Pour g´erer efficacement les tˆaches et les sprints tout en appliquant
la m´ethodologie Scrum,

nous avons opt´e pour Jira, une plateforme de gestion de projets tr` es
populaire. JIRA nous a

permis de planifier, suivre et g´erer les tˆaches tout au long du projet
en adoptant une approche

agile. Grˆace ` a cet outil, nous avons pu maintenir un suivi pr´ecis de
l'avancement du projet et

\begin{quote}
faciliter la communication au sein de l'´equipe.
\end{quote}

En tant qu'´equipe, nous avons collabor´e ` a l'´elaboration et ` a la
planification du backlog

produit, en identifiant et structurant les ´epis, les tˆaches et les
sous-tˆaches pour les deux

projets, ` a savoir Yejoura et ProTracker. Pour chacune de ces unit´es
de travail, nous avons

proc´ed´e ` a une estimation rigoureuse de la charge, en tenant compte
de leur complexit´e et de

leur priorit´e, afin de garantir une r´epartition ´equilibr´ee du
travail et une meilleure visibilit´
\end{minipage}} \\
\bottomrule()
\end{longtable}

\begin{quote}
sur l'avancement global du projet.
\end{quote}

La \textbf{figure 1.5} illustre le backlog du projet Pro-Tracker,
mettant en ´evidence l'ensemble des

epics, des tˆaches et des sous-tˆaches planifi´ees, ainsi que leur
niveau de priorit´e et les estimations

\begin{quote}
associ´ees.
\end{quote}

\includegraphics[width=6.61111in,height=2.68055in]{vertopal_f50a6211e6524672a7ad79ef0154e85e/media/image5.png}

\begin{longtable}[]{@{}
  >{\raggedright\arraybackslash}p{(\columnwidth - 0\tabcolsep) * \real{1.0000}}@{}}
\toprule()
\begin{minipage}[b]{\linewidth}\raggedright
Figure 1.5 \emph{-- Cycle de vie de la m´ethode SCRUM Pro-tracker}

la \textbf{figure 1.6} pr´esente le backlog du projet Yejoura,
structur´e selon les mˆemes principes

agiles, et permettant de visualiser clairement la r´epartition du
travail, les d´ependances entre

\begin{quote}
les tˆaches, ainsi que les charges estim´ees pour chaque ´el´ement.
\end{quote}
\end{minipage} \\
\midrule()
\endhead
\bottomrule()
\end{longtable}

15

\begin{longtable}[]{@{}
  >{\raggedright\arraybackslash}p{(\columnwidth - 0\tabcolsep) * \real{1.0000}}@{}}
\toprule()
\begin{minipage}[b]{\linewidth}\raggedright
\begin{quote}
Etude pr´ealable
\end{quote}
\end{minipage} \\
\midrule()
\endhead
\bottomrule()
\end{longtable}

\includegraphics[width=6.61111in,height=2.7375in]{vertopal_f50a6211e6524672a7ad79ef0154e85e/media/image6.png}

Figure 1.6 \emph{-- Cycle de vie de la m´ethode SCRUM Yejoura}

Ces deux backlogs ont ´et´e ´etablis de mani` ere collaborative par
l'´equipe, afin d'assurer une

\begin{quote}
planification coh´erente et une gestion efficace des sprints pour chacun
des projets.

\textbf{1.8.3} \textbf{Planification pr´evisionnelle}
\end{quote}

La gestion efficace du temps est un facteur d´eterminant pour le succ`
es de notre projet. Pour

cela, nous avons ´elabor´e un planning compos´e de diff´erentes ´etapes,
d´etaill´ees dans le table

\begin{quote}
suivant.
\end{quote}

\begin{longtable}[]{@{}
  >{\raggedright\arraybackslash}p{(\columnwidth - 0\tabcolsep) * \real{1.0000}}@{}}
\toprule()
\begin{minipage}[b]{\linewidth}\raggedright
Table 1.4 \emph{-- Planning des ´etapes du projet}

\begin{longtable}[]{@{}
  >{\raggedright\arraybackslash}p{(\columnwidth - 4\tabcolsep) * \real{0.3333}}
  >{\raggedright\arraybackslash}p{(\columnwidth - 4\tabcolsep) * \real{0.3333}}
  >{\raggedright\arraybackslash}p{(\columnwidth - 4\tabcolsep) * \real{0.3333}}@{}}
\toprule()
\begin{minipage}[b]{\linewidth}\raggedright
\begin{quote}
\textbf{Etapes´}
\end{quote}
\end{minipage} & \begin{minipage}[b]{\linewidth}\raggedright
\begin{quote}
\textbf{D´etails}
\end{quote}
\end{minipage} & \begin{minipage}[b]{\linewidth}\raggedright
\begin{quote}
\textbf{Dur´ee}
\end{quote}
\end{minipage} \\
\midrule()
\endhead
\begin{minipage}[t]{\linewidth}\raggedright
\begin{longtable}[]{@{}
  >{\raggedright\arraybackslash}p{(\columnwidth - 4\tabcolsep) * \real{0.3333}}
  >{\raggedright\arraybackslash}p{(\columnwidth - 4\tabcolsep) * \real{0.3333}}
  >{\raggedright\arraybackslash}p{(\columnwidth - 4\tabcolsep) * \real{0.3333}}@{}}
\toprule()
\begin{minipage}[b]{\linewidth}\raggedright
Analyse
\end{minipage} & \begin{minipage}[b]{\linewidth}\raggedright
et
\end{minipage} & \begin{minipage}[b]{\linewidth}\raggedright
architec-
\end{minipage} \\
\midrule()
\endhead
\bottomrule()
\end{longtable}

\begin{quote}
ture globale
\end{quote}
\end{minipage} & \begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
Pr´eparation du :\\
--- Document d'analyse\\
--- Backlog du produit\\
--- Document d'architecture--- Installation des outils
\end{quote}\strut
\end{minipage} & \begin{minipage}[t]{\linewidth}\raggedright
Du 3 F´evrier au 28

\begin{quote}
F´evrier 2025
\end{quote}
\end{minipage} \\
\begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
D´eveloppement
\end{quote}
\end{minipage} & \begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
Mise en œuvre des User Stories
\end{quote}
\end{minipage} & \begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
Du 3 Mars au 30 mai 2025
\end{quote}
\end{minipage} \\
\bottomrule()
\end{longtable}\strut
\end{minipage} \\
\midrule()
\endhead
\bottomrule()
\end{longtable}

16

\begin{longtable}[]{@{}
  >{\raggedright\arraybackslash}p{(\columnwidth - 2\tabcolsep) * \real{0.5000}}
  >{\raggedright\arraybackslash}p{(\columnwidth - 2\tabcolsep) * \real{0.5000}}@{}}
\toprule()
\multicolumn{2}{@{}>{\raggedright\arraybackslash}p{(\columnwidth - 2\tabcolsep) * \real{1.0000} + 2\tabcolsep}@{}}{%
\begin{minipage}[b]{\linewidth}\raggedright
\begin{quote}
Etude pr´ealable
\end{quote}
\end{minipage}} \\
\midrule()
\endhead
\begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
\textbf{1.9}
\end{quote}
\end{minipage} & \begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
\textbf{Conclusion}
\end{quote}
\end{minipage} \\
\multicolumn{2}{@{}>{\raggedright\arraybackslash}p{(\columnwidth - 2\tabcolsep) * \real{1.0000} + 2\tabcolsep}@{}}{%
\begin{minipage}[t]{\linewidth}\raggedright
En conclusion, ce chapitre nous a permis de pr´esenter l'organisme
d'accueil dans lequel s'est

d´eroul´e notre projet de fin d'´etudes, tout en situant le sujet dans
son contexte g´en´eral et en

pr´ecisant ses objectifs. Nous avons ensuite r´ealis´e une ´etude
pr´ealable incluant l'analyse de

l'existant, la mise en ´evidence de ses limites, ainsi que la
proposition d'une solution adapt´ee.

Par la suite, nous avons expos´e la m´ethodologie de travail adopt´ee,
avant de conclure par la

planification d´etaill´ee du projet. Le prochain chapitre, intitul´e
\textbf{Analyse et spc´ecification}

\textbf{des besoins}, portera sur une analyse approfondie des besoins
ainsi que sur la mod´elisation

\begin{quote}
de la solution envisag´ee.
\end{quote}
\end{minipage}} \\
\bottomrule()
\end{longtable}

17

\begin{longtable}[]{@{}
  >{\raggedright\arraybackslash}p{(\columnwidth - 2\tabcolsep) * \real{0.5000}}
  >{\raggedright\arraybackslash}p{(\columnwidth - 2\tabcolsep) * \real{0.5000}}@{}}
\toprule()
\begin{minipage}[b]{\linewidth}\raggedright
Chapitre\textbf{2}
\end{minipage} & \begin{minipage}[b]{\linewidth}\raggedright
\begin{quote}
\textbf{Analyse et sp´ecification des besoins}
\end{quote}
\end{minipage} \\
\midrule()
\endhead
\bottomrule()
\end{longtable}

18

\begin{longtable}[]{@{}
  >{\raggedright\arraybackslash}p{(\columnwidth - 2\tabcolsep) * \real{0.5000}}
  >{\raggedright\arraybackslash}p{(\columnwidth - 2\tabcolsep) * \real{0.5000}}@{}}
\toprule()
\multicolumn{2}{@{}>{\raggedright\arraybackslash}p{(\columnwidth - 2\tabcolsep) * \real{1.0000} + 2\tabcolsep}@{}}{%
\begin{minipage}[b]{\linewidth}\raggedright
\begin{quote}
Analyse et sp´ecification des besoins
\end{quote}
\end{minipage}} \\
\midrule()
\endhead
\begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
\textbf{2.1}
\end{quote}
\end{minipage} & \begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
\textbf{Introduction}
\end{quote}
\end{minipage} \\
\multicolumn{2}{@{}>{\raggedright\arraybackslash}p{(\columnwidth - 2\tabcolsep) * \real{1.0000} + 2\tabcolsep}@{}}{%
\begin{minipage}[t]{\linewidth}\raggedright
Apr` es avoir pr´esent´e le cadre g´en´eral de notre projet dans le
chapitre pr´ec´edent, nous abordons

a pr´esent la phase d'analyse des besoins et de conception. Cette ´etape
est cruciale pour assurer

le succ` es du projet, car elle permet d'identifier avec pr´ecision les
exigences fonctionnelles et

non fonctionnelles attendues par le client. Dans ce chapitre, nous
allons d´etailler ces besoins

en nous appuyant sur des outils de mod´elisation avanc´es, notamment le
diagramme de cas

d'utilisation. Ces outils nous aideront ` a mieux comprendre les
interactions entre les utilisateurs

\begin{quote}
et le syst` eme, ainsi qu'\,` a d´efinir l'architecture statique de ce
dernier.
\end{quote}
\end{minipage}} \\
\begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
\textbf{2.2}
\end{quote}
\end{minipage} & \begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
\textbf{Sp´ecification des besoins}
\end{quote}
\end{minipage} \\
\multicolumn{2}{@{}>{\raggedright\arraybackslash}p{(\columnwidth - 2\tabcolsep) * \real{1.0000} + 2\tabcolsep}@{}}{%
Cette section est d´edi´ee ` a l'identification et ` a la pr´esentation
approfondie des exigences fonc-} \\
\bottomrule()
\end{longtable}

\begin{quote}
tionnelles et non fonctionnelles du projet.

\textbf{2.2.1} \textbf{Identification des acteurs}
\end{quote}

Le syst` eme repose sur l'intervention de plusieurs acteurs fonctionnels
cl´es, chacun jouant un

rˆole d´eterminant dans le bon fonctionnement, la supervision et
l'optimisation des processus

\begin{quote}
de gestion de projet. Trois profils principaux interagissent avec la
plateforme :
\end{quote}

\textbf{L'administrateur} : Il d´etient l'ensemble des privil` eges sur
la plateforme. Il est charg´e de

la configuration globale du syst` eme, de la gestion des utilisateurs
(Invite des utililisateurs,

attribution de rˆoles), de l'organisation des permissions, ainsi que de
la supervision de la

s´ecurit´e, des performances et de la maintenance technique. Il veille
´egalement ` a la coh´erence

\begin{quote}
des donn´ees et au bon d´eroulement des op´erations internes.
\end{quote}

\textbf{L'utilisateur} : Il s'agit de l'intervenant principal dans
l'ex´ecution des tˆaches quotidiennes.

L'utilisateur peut consulter et ex´ecuter les tˆaches qui lui sont
assign´ees, enregistrer ses feuilles

de temps (timesheet), suivre l'´etat d'avancement de ses projets, et
interagir via les outils

collaboratifs tels que le backlog ou le diagramme de Gantt. Son p´erim`
etre d'action d´epend de

\begin{quote}
ses attributions au sein des projets.
\end{quote}

19

\begin{longtable}[]{@{}
  >{\raggedright\arraybackslash}p{(\columnwidth - 0\tabcolsep) * \real{1.0000}}@{}}
\toprule()
\begin{minipage}[b]{\linewidth}\raggedright
\begin{quote}
Analyse et sp´ecification des besoins
\end{quote}
\end{minipage} \\
\midrule()
\endhead
\textbf{Le Chef de Projet} : Il occupe un rˆole strat´egique, ax´e sur
la planification, la coor-

dination et le pilotage des projets. Il est responsable de la
r´epartition des tˆaches, du

suivi des d´elais, de la validation des jalons (milestones) et de
l'analyse des indicateurs de

performance ` a travers les tableaux de bord. Il collabore ´etroitement
avec les utilisateurs et su-

pervise le bon d´eroulement des diff´erentes phases du projet. Il peut
le seul supprimer le projet. \\
\bottomrule()
\end{longtable}

Table 2.1 \emph{-- Acteurs de l'application}

\begin{longtable}[]{@{}
  >{\raggedright\arraybackslash}p{(\columnwidth - 2\tabcolsep) * \real{0.5000}}
  >{\raggedright\arraybackslash}p{(\columnwidth - 2\tabcolsep) * \real{0.5000}}@{}}
\toprule()
\begin{minipage}[b]{\linewidth}\raggedright
\begin{quote}
\textbf{Acteurs}
\end{quote}
\end{minipage} & \begin{minipage}[b]{\linewidth}\raggedright
\begin{quote}
\textbf{Rˆole}
\end{quote}
\end{minipage} \\
\midrule()
\endhead
\begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
Administrateur
\end{quote}
\end{minipage} & \begin{minipage}[t]{\linewidth}\raggedright
Dispose de droits complets sur la plateforme. Il configure les pa-

ram` etres du syst` eme, invitez les utilisateurs et leurs permissions,

supervise la s´ecurit´e, la maintenance et la stabilit´e de l'environne-

ment applicatif. Il assure ´egalement la coh´erence et l'int´egrit´e des

\begin{quote}
donn´ees.
\end{quote}
\end{minipage} \\
\begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
Utilisateur
\end{quote}
\end{minipage} & \begin{minipage}[t]{\linewidth}\raggedright
Intervenant op´erationnel affect´e ` a un ou plusieurs projets. Il

consulte, ex´ecute et met ` a jour les tˆaches, renseigne les feuilles

de temps (timesheet), interagit avec les outils de suivi (backlog,

\begin{quote}
Gantt), et contribue activement ` a l'avancement des projets.
\end{quote}
\end{minipage} \\
\begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
Chef de Projet
\end{quote}
\end{minipage} & \begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
Responsable de la coordination des projets : Il planifie les activit´es,
assigne les tˆaches, suit les indicateurs de performance et valide les
jalons (milestones). Il veille ` a maintenir une communication fluide
entre les membres de l'´equipe et s'appuie sur des tableaux de bord pour
piloter efficacement les projets.
\end{quote}
\end{minipage} \\
\bottomrule()
\end{longtable}

\begin{longtable}[]{@{}
  >{\raggedright\arraybackslash}p{(\columnwidth - 2\tabcolsep) * \real{0.5000}}
  >{\raggedright\arraybackslash}p{(\columnwidth - 2\tabcolsep) * \real{0.5000}}@{}}
\toprule()
\begin{minipage}[b]{\linewidth}\raggedright
\begin{quote}
\textbf{2.2.2}
\end{quote}
\end{minipage} & \begin{minipage}[b]{\linewidth}\raggedright
\begin{quote}
\textbf{Identification des besoins fonctionnels}
\end{quote}
\end{minipage} \\
\midrule()
\endhead
\multicolumn{2}{@{}>{\raggedright\arraybackslash}p{(\columnwidth - 2\tabcolsep) * \real{1.0000} + 2\tabcolsep}@{}}{%
Les besoins fonctionnels repr´esentent la base essentielle de la
conception de toute solution

logicielle, en d´efinissant de mani` ere pr´ecise les actions que le
syst` eme doit permettre aux} \\
\bottomrule()
\end{longtable}

utilisateurs d'accomplir. Dans le cadre du projet \textbf{Pro-Tracker},
ces besoins sont cruciaux

\begin{longtable}[]{@{}
  >{\raggedright\arraybackslash}p{(\columnwidth - 0\tabcolsep) * \real{1.0000}}@{}}
\toprule()
\begin{minipage}[b]{\linewidth}\raggedright
pour garantir que les deux plateformes, \textbf{Pro-Tracker} et
\textbf{Yejoura}, r´epondent aux attentes

des parties prenantes tout en assurant une exp´erience utilisateur
fluide, coh´erente et adapt´ee

\begin{quote}
aux exigences m´etiers.

Les besoins fonctionnels identifi´es pour \textbf{Pro-Track} et
\textbf{Yejoura} sont les suivants :
\end{quote}
\end{minipage} \\
\midrule()
\endhead
\bottomrule()
\end{longtable}

20

\begin{longtable}[]{@{}
  >{\raggedright\arraybackslash}p{(\columnwidth - 0\tabcolsep) * \real{1.0000}}@{}}
\toprule()
\begin{minipage}[b]{\linewidth}\raggedright
\begin{quote}
Analyse et sp´ecification des besoins
\end{quote}
\end{minipage} \\
\midrule()
\endhead
\begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
--- \textbf{Gestion de l'authentification et des rˆoles} : Syst` eme
s´ecuris´e permettant la cr´eation

de compte, la connexion/d´econnexion, la r´einitialisation de mot de
passe, et l'invitation

d'utilisateurs par l'administrateur et les donn´ees des roles et des
permissions.
\end{quote}
\end{minipage} \\
\bottomrule()
\end{longtable}

\begin{quote}
--- \textbf{Gestion des profils utilisateurs} : Consultation et
modification des informations per-

sonnelles, changement d'avatar/mot de passe, et suivi des activit´es
r´ecentes pour chaque

utilisateur.

--- \textbf{Gestion des projets} : Cr´eation, modification, suppression
et archivage des projets,

avec d´efinition de jalons (milestones) et suivi du cycle de vie
complet.

--- \textbf{Affectation des membres et gestion des ´equipes} :
Assignation flexible des utili-

sateurs aux projets, gestion des ´equipes, suivi des charges de travail,
et ´evaluation des

performances (blˆames).

--- \textbf{Gestion des tˆaches} : Backlog interactif pour
cr´eer/modifier/supprimer des tˆaches, avec

priorisation et affectation aux membres. Diagramme de Gantt pour
visualiser l'avance-

ment et les d´ependances.

--- \textbf{Suivi des performances (KPIs)} : Tableaux de bord
personnalis´es avec indicateurs

cl´es (avancement, qualit´e, respect des d´elais) et rapports
d'activit´e pour chaque projet.

--- \textbf{Gestion budg´etaire et financi` ere} : Suivi des budgets
initiaux, d´epenses engag´ees

pour toute la soci´et´e. Gestion des clients, devis, factures et export
des donn´ees financi` eres

(CSV/PDF).

--- \textbf{Feuilles de temps (Timesheet)} : Enregistrement de feuille
de temps pour chaque

jour pour chaque utilisateur.

--- \textbf{Suivi des pr´esences} : Syst` eme automatis´e de Date
d'arriv´ee/Date de d´epart avec calcul

de scores de ponctualit´e et statuts journaliers (retard, absent, etc.).

--- \textbf{Notifications} : Alertes en temps r´eel pour les mises ` a
jour de projets, tˆaches assign´ees,

changements de statuts et ´ech´eances critiques.

--- \textbf{Chatbot} : Assistant virtuel pour r´epondre aux questions
fr´equentes (FAQ) et fournir

des informations rapides sur l'´etat des projets/tˆaches.

--- \textbf{Internationalisation} : Basculer entre plusieurs langues
(fran¸cais et anglais) pour adap-

ter l'interface aux besoins des utilisateurs.
\end{quote}

\begin{longtable}[]{@{}
  >{\raggedright\arraybackslash}p{(\columnwidth - 0\tabcolsep) * \real{1.0000}}@{}}
\toprule()
\begin{minipage}[b]{\linewidth}\raggedright
Ces besoins fonctionnels doivent ˆetre int´egr´es de mani` ere fluide
dans l'interface de chaque

plateforme, afin d'offrir aux utilisateurs une exp´erience coh´erente et
intuitive. Chaque fonc-

tionnalit´e est con¸cue pour soutenir l'optimisation de la gestion de
projet, de sa planification

\begin{quote}
a son ach` evement, en garantissant une visibilit´e maximale et un
contrˆole efficace.
\end{quote}
\end{minipage} \\
\midrule()
\endhead
\bottomrule()
\end{longtable}

21

\begin{longtable}[]{@{}
  >{\raggedright\arraybackslash}p{(\columnwidth - 0\tabcolsep) * \real{1.0000}}@{}}
\toprule()
\begin{minipage}[b]{\linewidth}\raggedright
\begin{quote}
Analyse et sp´ecification des besoins
\end{quote}
\end{minipage} \\
\midrule()
\endhead
L'identification de ces besoins nous permet de guider la conception et
le d´eveloppement de la

solution, en veillant ` a ce que les fonctionnalit´es essentielles
soient correctement impl´ement´ees

pour r´epondre aux exigences sp´ecifiques et aux objectifs des deux
projets \textbf{Pro-Track} et \textbf{Ye-} \\
\bottomrule()
\end{longtable}

\begin{quote}
\textbf{joura}.

\textbf{2.2.3} \textbf{Identification des besoins non fonctionnels}
\end{quote}

Pour r´epondre aux exigences de s´ecurit´e et de performance de nos
clients, plusieurs mesures

\begin{quote}
essentielles ont ´et´e mises en place dans le cadre du projet
\textbf{Pro-Track} et \textbf{Yejoura}.
\end{quote}

Les besoins non fonctionnels repr´esentent les crit` eres de
performance, de s´ecurit´e, de fiabilit´

et d'ergonomie auxquels les deux plateformes doivent r´epondre, en
compl´ement des besoins

fonctionnels. Ces crit` eres sont essentiels pour garantir la qualit´e
globale des syst` emes et leur

\begin{quote}
ad´equation aux attentes des utilisateurs et des parties prenantes.

--- \textbf{Ergonomie} : Afin de garantir une facilit´e d'exploitation
et une rapidit´e de service, la

plateforme doit disposer d'interfaces intuitives, claires et conviviales
pour tous les profils

d'utilisateurs.

--- \textbf{S´ecurit´e} : L'application doit assurer la
confidentialit´e, l'int´egrit´e et la disponibilit´e des

donn´ees. Cela inclut l'utilisation de m´ecanismes de chiffrement pour
les donn´ees sen-

sibles, des protocoles d'authentification robustes, ainsi que la gestion
des droits d'acc` es

selon les rˆoles.

--- \textbf{Performance} :L'application doit offrir une r´eactivit´e
optimale aux actions des utilisa-

teurs, avec des temps de chargement r´eduits et des performances
stables, mˆeme en cas de

nombreuses connexions simultan´ees. Le syst` eme doit ´egalement assurer
une mise ` a jour

des donn´ees en temps r´eel. Par exemple, lorsqu'un utilisateur effectue
une action telle

que la cr´eation d'un projet ou la modification d'une tˆache sur la
plateforme Yejoura,

cette information doit ˆetre instantan´ement visible sur Protracker.

--- \textbf{Maintenabilit´e} : Le syst` eme doit ˆetre con¸cu de mani`
ere modulaire afin de faciliter sa

maintenance et son ´evolution future. Il doit permettre une correction
rapide des erreurs,

une mise ` a jour facilit´ee, ainsi qu'une documentation claire du code.
\end{quote}

Ces besoins non fonctionnels sont cruciaux pour garantir la robustesse,
la s´ecurit´e, et la per-

formance des solutions \textbf{Pro-Tracker} et \textbf{Yejoura}. Ils
influencent directement la conception

\begin{longtable}[]{@{}
  >{\raggedright\arraybackslash}p{(\columnwidth - 0\tabcolsep) * \real{1.0000}}@{}}
\toprule()
\begin{minipage}[b]{\linewidth}\raggedright
technique et l'architecture des syst` emes, et doivent ˆetre int´egr´es
d` es les premi` eres ´etapes du
\end{minipage} \\
\midrule()
\endhead
\bottomrule()
\end{longtable}

22

\begin{longtable}[]{@{}
  >{\raggedright\arraybackslash}p{(\columnwidth - 2\tabcolsep) * \real{0.5000}}
  >{\raggedright\arraybackslash}p{(\columnwidth - 2\tabcolsep) * \real{0.5000}}@{}}
\toprule()
\multicolumn{2}{@{}>{\raggedright\arraybackslash}p{(\columnwidth - 2\tabcolsep) * \real{1.0000} + 2\tabcolsep}@{}}{%
\begin{minipage}[b]{\linewidth}\raggedright
\begin{quote}
Analyse et sp´ecification des besoins
\end{quote}
\end{minipage}} \\
\midrule()
\endhead
\multicolumn{2}{@{}>{\raggedright\arraybackslash}p{(\columnwidth - 2\tabcolsep) * \real{1.0000} + 2\tabcolsep}@{}}{%
\begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
d´eveloppement pour assurer la satisfaction des utilisateurs et la
p´erennit´e des plateformes.
\end{quote}
\end{minipage}} \\
\begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
\textbf{2.3}
\end{quote}
\end{minipage} & \begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
\textbf{Pilotage du projet avec Scrum}
\end{quote}
\end{minipage} \\
\begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
\textbf{2.3.1}
\end{quote}
\end{minipage} & \begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
\textbf{Fonctionnalit´es du Backlog}
\end{quote}
\end{minipage} \\
\multicolumn{2}{@{}>{\raggedright\arraybackslash}p{(\columnwidth - 2\tabcolsep) * \real{1.0000} + 2\tabcolsep}@{}}{%
Le \textbf{Product Backlog} regroupe l'ensemble des fonctionnalit´es ` a
d´evelopper, prioris´ees selon la

valeur m´etier. Il ´evolue tout au long du projet. Voici les principales
fonctionnalit´es identifi´ees :} \\
\bottomrule()
\end{longtable}

\begin{quote}
--- \textbf{Gestion des projets}

Cr´eation, modification et suivi des projets avec affectation des
´equipes et ´ech´eances.

--- \textbf{Suivi des tˆaches}

Cr´eation de tˆaches, affectation ` a des membres, suivi de l'avancement
et gestion des

d´ependances.

--- \textbf{Gestion financi´ere}

Visualisation en temps r´eel des d´epenses, budgets allou´es, et ´ecarts
par projet.

--- \textbf{Synchronisation projet-finances}

Mise ` a jour automatique des indicateurs financiers en fonction de
l'avancement

op´erationnel.

--- \textbf{Notifications et alertes}

Alertes sur d´epassements budg´etaires, retards ou changements
critiques.

--- \textbf{Gestion des utilisateurs et rˆoles}

Contrˆole des acc` es selon les profils (admin, chef de projet,
utilisateurs. . .).

\textbf{2.3.2} \textbf{Diagramme de cas d'utilisation}
\end{quote}

Le diagramme de cas d'utilisation ci-dessous (Figure 2.1) illustre les
principales interactions

\begin{longtable}[]{@{}
  >{\raggedright\arraybackslash}p{(\columnwidth - 0\tabcolsep) * \real{1.0000}}@{}}
\toprule()
\begin{minipage}[b]{\linewidth}\raggedright
entre les acteurs identifi´es et le syst` eme de suivi de projets et
gestion financi` ere. Les acteurs

sont l'utilisateur, l'administrateur et le chef de projet. Chaque acteur
interagit avec le syst` eme

\begin{quote}
pour accomplir des tˆaches sp´ecifiques.
\end{quote}
\end{minipage} \\
\midrule()
\endhead
\bottomrule()
\end{longtable}

23

\begin{longtable}[]{@{}
  >{\raggedright\arraybackslash}p{(\columnwidth - 0\tabcolsep) * \real{1.0000}}@{}}
\toprule()
\begin{minipage}[b]{\linewidth}\raggedright
\begin{quote}
Analyse et sp´ecification des besoins
\end{quote}
\end{minipage} \\
\midrule()
\endhead
\bottomrule()
\end{longtable}

\includegraphics[width=7.27222in,height=7.09028in]{vertopal_f50a6211e6524672a7ad79ef0154e85e/media/image7.png}

\begin{quote}
Figure 2.1 \emph{-- Diagramme de cas d'utilisation}
\end{quote}

24

\begin{longtable}[]{@{}
  >{\raggedright\arraybackslash}p{(\columnwidth - 0\tabcolsep) * \real{1.0000}}@{}}
\toprule()
\begin{minipage}[b]{\linewidth}\raggedright
\begin{quote}
Analyse et sp´ecification des besoins
\end{quote}
\end{minipage} \\
\midrule()
\endhead
\bottomrule()
\end{longtable}

\begin{quote}
\textbf{Acteur : Administrateur}
\end{quote}

L'administrateur dispose de droits ´etendus sur l'ensemble du syst` eme.
Il peut configurer

la plateforme, g´erer les utilisateurs, attribuer des rˆoles, g´erer les
permissions et superviser

l'ensemble des op´erations de l'application. L'administrateur peut
´egalement invitez des

\begin{quote}
utilisateurs, ainsi que d´efinir leurs rˆoles et permissions
sp´ecifiques.

\textbf{Acteur : Utilisateur}
\end{quote}

l'utilisateur peut consulter et suivre l'avancement des projets, g´erer
les tˆaches qui lui sont

affect´ees. mettre ` a jour les statuts des tˆaches et enregistrer ses
feuilles de temps (timesheet) .

\textbf{Acteur : Chef de Projet} Le chef de projet a un rˆole de
coordination et de supervision. Il

a acc` es ` a toutes les fonctionnalit´es d'un utilisateur, mais dispose
en plus d'outils sp´ecifiques

pour g´erer et planifier les projets. Il est responsable de
l'affectation des tˆaches, ainsi que du

suivi des indicateurs de performance. Il peut ´egalement valider les
livrables et s'assurer du

\begin{quote}
respect des jalons du projet.
\end{quote}

\textbf{Authentification} Pour acc´eder aux fonctionnalit´es du syst`
eme, tous les utilisateurs doivent

s'authentifier ` a l'aide de leurs donn´ees d'identification (adresse
´electronique et mot de passe).

Ce diagramme de cas d'utilisation illustre les interactions essentielles
entre les acteurs princi-

\begin{longtable}[]{@{}
  >{\raggedright\arraybackslash}p{(\columnwidth - 2\tabcolsep) * \real{0.5000}}
  >{\raggedright\arraybackslash}p{(\columnwidth - 2\tabcolsep) * \real{0.5000}}@{}}
\toprule()
\multicolumn{2}{@{}>{\raggedright\arraybackslash}p{(\columnwidth - 2\tabcolsep) * \real{1.0000} + 2\tabcolsep}@{}}{%
\begin{minipage}[b]{\linewidth}\raggedright
paux et le syst` eme, en mettant en ´evidence les fonctionnalit´es
accessibles ` a chaque rˆole, ainsi

\begin{quote}
que les m´ecanismes de s´ecurit´e associ´es.
\end{quote}
\end{minipage}} \\
\midrule()
\endhead
\begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
\textbf{2.3.3}
\end{quote}
\end{minipage} & \begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
\textbf{Planification des sprints}
\end{quote}
\end{minipage} \\
\multicolumn{2}{@{}>{\raggedright\arraybackslash}p{(\columnwidth - 2\tabcolsep) * \real{1.0000} + 2\tabcolsep}@{}}{%
\begin{minipage}[t]{\linewidth}\raggedright
Nous pr´esentons dans cette partie le planning du travail et les dur´ees
des sprints (figure 2.2).

Nous avons fait une estimation de la dur´ee du projet. ` A la lumi` ere
de cette estimation, nous

avons choisi d'adopter une planification de cinq sprints, pr´ec´ed´es
d'un Sprint 0. Ce sprint

initial est d´edi´e ` a la mise en place de l'environnement technique, `
a la configuration des outils

de d´eveloppement, ` a la pr´eparation de l'architecture du projet,
ainsi qu'\,` a la clarification des

\begin{quote}
rˆoles au sein de l'´equipe.
\end{quote}
\end{minipage}} \\
\bottomrule()
\end{longtable}

25

\begin{longtable}[]{@{}
  >{\raggedright\arraybackslash}p{(\columnwidth - 0\tabcolsep) * \real{1.0000}}@{}}
\toprule()
\begin{minipage}[b]{\linewidth}\raggedright
\begin{quote}
Analyse et sp´ecification des besoins
\end{quote}
\end{minipage} \\
\midrule()
\endhead
\bottomrule()
\end{longtable}

\includegraphics[width=6.61111in,height=4.43194in]{vertopal_f50a6211e6524672a7ad79ef0154e85e/media/image8.png}

Figure 2.2 \emph{-- Planification des sprints}

\begin{quote}
\textbf{2.4} \textbf{Architecture de la solution}
\end{quote}

Cette section est d´edi´ee ` a la pr´esentation et ` a la description
d´etaill´ee de l'architecture de notre

solution. Elle vise ` a fournir une vue d'ensemble claire et structur´ee
des composants principaux

du syst` eme, de leurs interactions ainsi que des choix technologiques
adopt´es, afin de mieux

\begin{quote}
comprendre la conception globale de l'application.
\end{quote}

\begin{longtable}[]{@{}
  >{\raggedright\arraybackslash}p{(\columnwidth - 2\tabcolsep) * \real{0.5000}}
  >{\raggedright\arraybackslash}p{(\columnwidth - 2\tabcolsep) * \real{0.5000}}@{}}
\toprule()
\begin{minipage}[b]{\linewidth}\raggedright
\begin{quote}
\textbf{2.4.1}
\end{quote}
\end{minipage} & \begin{minipage}[b]{\linewidth}\raggedright
\begin{quote}
\textbf{Architecture globale de la solution}
\end{quote}
\end{minipage} \\
\midrule()
\endhead
\multicolumn{2}{@{}>{\raggedright\arraybackslash}p{(\columnwidth - 2\tabcolsep) * \real{1.0000} + 2\tabcolsep}@{}}{%
\begin{minipage}[t]{\linewidth}\raggedright
L'architecture d'un syst` eme informatique d´esigne sa structure
globale, c'est-` a-dire l'organi-

sation des diff´erents composants logiciels et mat´eriels, ainsi que les
relations qui existent

entre eux. Elle constitue un cadre fondamental pour assurer la
coh´erence, la maintenabilit´e et

\begin{quote}
l'´evolutivit´e du syst` eme.
\end{quote}

La figure 2.3 illustre l'architecture globale de notre plateforme. Elle
met en ´evidence les princi-
\end{minipage}} \\
\bottomrule()
\end{longtable}

26

\begin{longtable}[]{@{}
  >{\raggedright\arraybackslash}p{(\columnwidth - 0\tabcolsep) * \real{1.0000}}@{}}
\toprule()
\begin{minipage}[b]{\linewidth}\raggedright
\begin{quote}
Analyse et sp´ecification des besoins
\end{quote}
\end{minipage} \\
\midrule()
\endhead
\begin{minipage}[t]{\linewidth}\raggedright
paux composants du syst` eme, leurs rˆoles respectifs ainsi que les
interactions entre ces ´el´ements.

Cette repr´esentation permet de mieux comprendre la structure interne de
la solution et les

\begin{quote}
flux de donn´ees qui la traversent.
\end{quote}
\end{minipage} \\
\bottomrule()
\end{longtable}

\includegraphics[width=3.70278in,height=3.70139in]{vertopal_f50a6211e6524672a7ad79ef0154e85e/media/image9.png}

Figure 2.3 \emph{-- Architecture globale de la solution}

\begin{quote}
--- \textbf{Frontend (Next.js)} : L'interface utilisateur construite
avec Next.js communique avec

le backend via des requˆetes HTTP.

--- \textbf{Backend (Node.js)} :Node.js g` ere la gestion du contenu et
la logique m´etier. Il expose

une API RESTful.

--- \textbf{Base de donn´ees (PostgreSQL)} : Node.js se connecte ` a une
base de donn´ees Post-

greSQL pour stocker et r´ecup´erer les donn´ees de l'application.

Le flux de communication implique :
\end{quote}

\begin{longtable}[]{@{}
  >{\raggedright\arraybackslash}p{(\columnwidth - 0\tabcolsep) * \real{1.0000}}@{}}
\toprule()
\begin{minipage}[b]{\linewidth}\raggedright
\begin{quote}
--- \textbf{HTTP} : Utilis´e pour transf´erer les donn´ees entre Next.js
et Node.js, ainsi qu'entre

Node.js et la base de donn´ees.

--- \textbf{API} : Next.js acc` ede aux points de terminaison API de
Node.js.

--- \textbf{JSON} : Les donn´ees sont ´echang´ees au format JSON.

--- \textbf{Token d'acc` es} : Utilis´e pour l'authentification,
g´en´eralement stock´e dans le stockage

local ou les cookies et transmis dans les en-tˆetes des requˆetes.
\end{quote}
\end{minipage} \\
\midrule()
\endhead
\bottomrule()
\end{longtable}

27

\begin{longtable}[]{@{}
  >{\raggedright\arraybackslash}p{(\columnwidth - 2\tabcolsep) * \real{0.5000}}
  >{\raggedright\arraybackslash}p{(\columnwidth - 2\tabcolsep) * \real{0.5000}}@{}}
\toprule()
\multicolumn{2}{@{}>{\raggedright\arraybackslash}p{(\columnwidth - 2\tabcolsep) * \real{1.0000} + 2\tabcolsep}@{}}{%
\begin{minipage}[b]{\linewidth}\raggedright
\begin{quote}
Analyse et sp´ecification des besoins
\end{quote}
\end{minipage}} \\
\midrule()
\endhead
\multicolumn{2}{@{}>{\raggedright\arraybackslash}p{(\columnwidth - 2\tabcolsep) * \real{1.0000} + 2\tabcolsep}@{}}{%
\begin{minipage}[t]{\linewidth}\raggedright
Cette configuration est courante pour les applications web d´ecoupl´ees
qui n´ecessitent une

\begin{quote}
architecture modulaire et ´evolutive.
\end{quote}
\end{minipage}} \\
\begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
\textbf{2.4.2}
\end{quote}
\end{minipage} & \begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
\textbf{Architecture logique de la solution}
\end{quote}
\end{minipage} \\
\multicolumn{2}{@{}>{\raggedright\arraybackslash}p{(\columnwidth - 2\tabcolsep) * \real{1.0000} + 2\tabcolsep}@{}}{%
L'architecture logique d´ecrit les couches fonctionnelles de
l'application, ind´ependamment des

consid´erations physiques ou technologiques. Elle clarifie la
structuration du syst` eme en mo-} \\
\bottomrule()
\end{longtable}

\begin{quote}
dules logiques :

--- \textbf{Couche pr´esentation (Frontend)} : G` ere l'affichage,
l'interaction avec l'utilisateur et

la navigation. Elle consomme les services expos´es par l'API.

--- \textbf{Couche m´etier (Backend)} : Contient la logique m´etier, les
r` egles de gestion, et les

contrˆoleurs qui traitent les requˆetes et orchestrent les donn´ees.

--- \textbf{Couche service (API REST)} : Sert d'interface de
communication entre le frontend
\end{quote}

\begin{longtable}[]{@{}
  >{\raggedright\arraybackslash}p{(\columnwidth - 2\tabcolsep) * \real{0.5000}}
  >{\raggedright\arraybackslash}p{(\columnwidth - 2\tabcolsep) * \real{0.5000}}@{}}
\toprule()
\multicolumn{2}{@{}>{\raggedright\arraybackslash}p{(\columnwidth - 2\tabcolsep) * \real{1.0000} + 2\tabcolsep}@{}}{%
\begin{minipage}[b]{\linewidth}\raggedright
\begin{quote}
et le backend. Elle assure l'encapsulation des fonctionnalit´es et la
s´ecurisation des acc` es.

--- \textbf{Couche donn´ees (Base de donn´ees)} : Responsable de la
persistance des informations

et de leur structuration. Utilise des requˆetes SQL pour interagir avec
PostgreSQL.
\end{quote}

Cette organisation en couches permet une meilleure s´eparation des
responsabilit´es, facilitant

\begin{quote}
la maintenance, l'´evolutivit´e, ainsi que les tests unitaires et
d'int´egration.
\end{quote}
\end{minipage}} \\
\midrule()
\endhead
\begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
\textbf{2.5}
\end{quote}
\end{minipage} & \begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
\textbf{Environnement de travail}
\end{quote}
\end{minipage} \\
\multicolumn{2}{@{}>{\raggedright\arraybackslash}p{(\columnwidth - 2\tabcolsep) * \real{1.0000} + 2\tabcolsep}@{}}{%
\begin{minipage}[t]{\linewidth}\raggedright
L'environnement de travail joue un rˆole crucial dans la r´eussite de
tout projet, influen¸cant

directement la productivit´e, la collaboration et le bien-ˆetre des
membres de l'´equipe. Pour

\begin{quote}
notre projet de d´eveloppement de deux plateforme de gestion et de suivi
des projets.
\end{quote}
\end{minipage}} \\
\begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
\textbf{2.5.1}
\end{quote}
\end{minipage} & \begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
\textbf{Environnement mat´eriel}
\end{quote}
\end{minipage} \\
\multicolumn{2}{@{}>{\raggedright\arraybackslash}p{(\columnwidth - 2\tabcolsep) * \real{1.0000} + 2\tabcolsep}@{}}{%
\begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
Pour assurer un d´eveloppement fluide et efficace, nous avons utilis´e
un mat´eriel adapt´e :

--- \textbf{Ordinateur personnel} : Un PC portable performant ´equip´e
d'un processeur Intel Core

i7, 8 Go de RAM et un disque SSD de 1000 Go, permettant une bonne
r´eactivit´e lors

du d´eveloppement et des tests.
\end{quote}
\end{minipage}} \\
\bottomrule()
\end{longtable}

28

\begin{longtable}[]{@{}
  >{\raggedright\arraybackslash}p{(\columnwidth - 2\tabcolsep) * \real{0.5000}}
  >{\raggedright\arraybackslash}p{(\columnwidth - 2\tabcolsep) * \real{0.5000}}@{}}
\toprule()
\multicolumn{2}{@{}>{\raggedright\arraybackslash}p{(\columnwidth - 2\tabcolsep) * \real{1.0000} + 2\tabcolsep}@{}}{%
\begin{minipage}[b]{\linewidth}\raggedright
\begin{quote}
Analyse et sp´ecification des besoins
\end{quote}
\end{minipage}} \\
\midrule()
\endhead
\begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
\textbf{2.5.2}
\end{quote}
\end{minipage} & \begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
\textbf{Environnement de d´eveloppement}
\end{quote}
\end{minipage} \\
\multicolumn{2}{@{}>{\raggedright\arraybackslash}p{(\columnwidth - 2\tabcolsep) * \real{1.0000} + 2\tabcolsep}@{}}{%
\begin{minipage}[t]{\linewidth}\raggedright
Le d´eveloppement de la plateforme s'est d´eroul´e dans un environnement
logiciel moderne et

\begin{quote}
adapt´e aux besoins du projet, compos´e des outils suivants :

--- \textbf{Enterprise Architect} {[}1{]} : une plate-forme de
mod´elisation et de conception sophis-

tiqu´ee et conviviale adapt´ee aux besoins modernes, alliant exploration
et innovation.

Saisissez le pr´esent, envisagez l'avenir et construisez un mod` ele
dynamique ` a la fois ro-

buste et ambitieux. Votre mod` ele servira de boussole, renfor¸cant
votre vision, les objectifs

de votre ´equipe et la trajectoire de votre entreprise.
\end{quote}
\end{minipage}} \\
\bottomrule()
\end{longtable}

\begin{longtable}[]{@{}
  >{\raggedright\arraybackslash}p{(\columnwidth - 0\tabcolsep) * \real{1.0000}}@{}}
\toprule()
\begin{minipage}[b]{\linewidth}\raggedright
\includegraphics[width=0.99167in,height=0.74167in]{vertopal_f50a6211e6524672a7ad79ef0154e85e/media/image10.png}
\end{minipage} \\
\midrule()
\endhead
\bottomrule()
\end{longtable}

Figure 2.4 \emph{-- Logo Enterprise Architect}

\begin{longtable}[]{@{}
  >{\raggedright\arraybackslash}p{(\columnwidth - 0\tabcolsep) * \real{1.0000}}@{}}
\toprule()
\begin{minipage}[b]{\linewidth}\raggedright
\begin{quote}
--- \textbf{Visual Studio Code} {[}2{]} : Visual Studio Code est con¸cu
dans un souci d'extensibilit´e.

De l'interface utilisateur ` a l'exp´erience d'´edition, presque chaque
´el´ement de VS Code

peut ˆetre personnalis´e et am´elior´e grˆace ` a l'API d'extension.
\end{quote}
\end{minipage} \\
\midrule()
\endhead
\bottomrule()
\end{longtable}

\begin{longtable}[]{@{}
  >{\raggedright\arraybackslash}p{(\columnwidth - 0\tabcolsep) * \real{1.0000}}@{}}
\toprule()
\begin{minipage}[b]{\linewidth}\raggedright
\includegraphics[width=0.99167in,height=0.99167in]{vertopal_f50a6211e6524672a7ad79ef0154e85e/media/image11.png}
\end{minipage} \\
\midrule()
\endhead
\bottomrule()
\end{longtable}

Figure 2.5 \emph{-- Logo Visual Studio Code}

\begin{longtable}[]{@{}
  >{\raggedright\arraybackslash}p{(\columnwidth - 0\tabcolsep) * \real{1.0000}}@{}}
\toprule()
\begin{minipage}[b]{\linewidth}\raggedright
\begin{quote}
--- \textbf{Postman} {[}3{]} : outil intuitif pour tester les API,
permettant d'envoyer des requˆetes

HTTP et d'analyser les r´eponses.
\end{quote}
\end{minipage} \\
\midrule()
\endhead
\bottomrule()
\end{longtable}

\begin{longtable}[]{@{}
  >{\raggedright\arraybackslash}p{(\columnwidth - 0\tabcolsep) * \real{1.0000}}@{}}
\toprule()
\begin{minipage}[b]{\linewidth}\raggedright
\includegraphics[width=0.99167in,height=0.99167in]{vertopal_f50a6211e6524672a7ad79ef0154e85e/media/image12.png}
\end{minipage} \\
\midrule()
\endhead
\bottomrule()
\end{longtable}

Figure 2.6 \emph{-- Logo Postman}

29

\begin{longtable}[]{@{}
  >{\raggedright\arraybackslash}p{(\columnwidth - 0\tabcolsep) * \real{1.0000}}@{}}
\toprule()
\begin{minipage}[b]{\linewidth}\raggedright
\begin{quote}
Analyse et sp´ecification des besoins
\end{quote}
\end{minipage} \\
\midrule()
\endhead
\begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
--- \textbf{GitHub} {[}4{]} : plateforme d'h´ebergement de code et de
collaboration bas´ee sur Git, faci-

litant la gestion de versions et le travail en ´equipe.
\end{quote}
\end{minipage} \\
\bottomrule()
\end{longtable}

\begin{longtable}[]{@{}
  >{\raggedright\arraybackslash}p{(\columnwidth - 0\tabcolsep) * \real{1.0000}}@{}}
\toprule()
\begin{minipage}[b]{\linewidth}\raggedright
\includegraphics[width=0.99167in,height=0.99167in]{vertopal_f50a6211e6524672a7ad79ef0154e85e/media/image13.png}
\end{minipage} \\
\midrule()
\endhead
\bottomrule()
\end{longtable}

Figure 2.7 \emph{-- Logo GitHub}

\begin{quote}
\textbf{2.5.3} \textbf{Environnement logiciel}

Le projet repose sur plusieurs technologies et frameworks pour r´epondre
aux besoins fonction-nels et techniques :
\end{quote}

\begin{longtable}[]{@{}
  >{\raggedright\arraybackslash}p{(\columnwidth - 0\tabcolsep) * \real{1.0000}}@{}}
\toprule()
\begin{minipage}[b]{\linewidth}\raggedright
\begin{quote}
--- \textbf{PostgreSQL} {[}5{]} : syst` eme de gestion de base de
donn´ees relationnelle puissant et open

source, permettant de stocker et g´erer les donn´ees complexes du
projet.
\end{quote}
\end{minipage} \\
\midrule()
\endhead
\bottomrule()
\end{longtable}

\begin{longtable}[]{@{}
  >{\raggedright\arraybackslash}p{(\columnwidth - 0\tabcolsep) * \real{1.0000}}@{}}
\toprule()
\begin{minipage}[b]{\linewidth}\raggedright
\includegraphics[width=0.99167in,height=0.55694in]{vertopal_f50a6211e6524672a7ad79ef0154e85e/media/image14.png}
\end{minipage} \\
\midrule()
\endhead
\bottomrule()
\end{longtable}

Figure 2.8 \emph{-- Logo PostgreSQL}

\begin{longtable}[]{@{}
  >{\raggedright\arraybackslash}p{(\columnwidth - 0\tabcolsep) * \real{1.0000}}@{}}
\toprule()
\begin{minipage}[b]{\linewidth}\raggedright
\begin{quote}
--- \textbf{Next.js} {[}6{]} : framework React facilitant la cr´eation
d'applications web full-stack, opti-

mis´ees et performantes.
\end{quote}
\end{minipage} \\
\midrule()
\endhead
\bottomrule()
\end{longtable}

\begin{longtable}[]{@{}
  >{\raggedright\arraybackslash}p{(\columnwidth - 0\tabcolsep) * \real{1.0000}}@{}}
\toprule()
\begin{minipage}[b]{\linewidth}\raggedright
\includegraphics[width=0.99167in,height=0.66111in]{vertopal_f50a6211e6524672a7ad79ef0154e85e/media/image15.png}
\end{minipage} \\
\midrule()
\endhead
\bottomrule()
\end{longtable}

Figure 2.9 \emph{-- Logo Next.js}

\begin{longtable}[]{@{}
  >{\raggedright\arraybackslash}p{(\columnwidth - 0\tabcolsep) * \real{1.0000}}@{}}
\toprule()
\begin{minipage}[b]{\linewidth}\raggedright
\begin{quote}
--- \textbf{Node.js} {[}7{]} : plateforme JavaScript cˆot´e serveur
permettant d'ex´ecuter du code serveur

de mani` ere efficace.
\end{quote}
\end{minipage} \\
\midrule()
\endhead
\bottomrule()
\end{longtable}

\begin{longtable}[]{@{}
  >{\raggedright\arraybackslash}p{(\columnwidth - 0\tabcolsep) * \real{1.0000}}@{}}
\toprule()
\begin{minipage}[b]{\linewidth}\raggedright
\includegraphics[width=0.99167in,height=0.60833in]{vertopal_f50a6211e6524672a7ad79ef0154e85e/media/image16.png}
\end{minipage} \\
\midrule()
\endhead
\bottomrule()
\end{longtable}

Figure 2.10 \emph{-- Logo Node.js}

30

\begin{longtable}[]{@{}
  >{\raggedright\arraybackslash}p{(\columnwidth - 0\tabcolsep) * \real{1.0000}}@{}}
\toprule()
\begin{minipage}[b]{\linewidth}\raggedright
\begin{quote}
Analyse et sp´ecification des besoins
\end{quote}
\end{minipage} \\
\midrule()
\endhead
\bottomrule()
\end{longtable}

\begin{quote}
--- \textbf{TypeScript} {[}8{]} : sur-ensemble de JavaScript offrant un
typage statique, facilitant la

maintenance et la robustesse du code.
\end{quote}

\begin{longtable}[]{@{}
  >{\raggedright\arraybackslash}p{(\columnwidth - 0\tabcolsep) * \real{1.0000}}@{}}
\toprule()
\begin{minipage}[b]{\linewidth}\raggedright
\includegraphics[width=0.99167in,height=0.99167in]{vertopal_f50a6211e6524672a7ad79ef0154e85e/media/image17.png}
\end{minipage} \\
\midrule()
\endhead
\bottomrule()
\end{longtable}

Figure 2.11 \emph{-- Logo TypeScript}

\begin{quote}
--- \textbf{Ant Design} {[}9{]} : biblioth` eque de composants UI
´el´egants et coh´erents pour construire

l'interface utilisateur.
\end{quote}

\begin{longtable}[]{@{}
  >{\raggedright\arraybackslash}p{(\columnwidth - 0\tabcolsep) * \real{1.0000}}@{}}
\toprule()
\begin{minipage}[b]{\linewidth}\raggedright
\includegraphics[width=0.99167in,height=0.99028in]{vertopal_f50a6211e6524672a7ad79ef0154e85e/media/image18.png}
\end{minipage} \\
\midrule()
\endhead
\bottomrule()
\end{longtable}

Figure 2.12 \emph{-- Logo Ant Design}

\begin{quote}
--- \textbf{Tailwind CSS} {[}10{]} : framework CSS utilitaire permettant
de concevoir rapidement des
\end{quote}

\begin{longtable}[]{@{}
  >{\raggedright\arraybackslash}p{(\columnwidth - 0\tabcolsep) * \real{1.0000}}@{}}
\toprule()
\begin{minipage}[b]{\linewidth}\raggedright
\begin{quote}
interfaces personnalis´ees.
\end{quote}
\end{minipage} \\
\midrule()
\endhead
\bottomrule()
\end{longtable}

\begin{longtable}[]{@{}
  >{\raggedright\arraybackslash}p{(\columnwidth - 0\tabcolsep) * \real{1.0000}}@{}}
\toprule()
\begin{minipage}[b]{\linewidth}\raggedright
\includegraphics[width=0.99167in,height=0.99167in]{vertopal_f50a6211e6524672a7ad79ef0154e85e/media/image19.png}
\end{minipage} \\
\midrule()
\endhead
\bottomrule()
\end{longtable}

Figure 2.13 \emph{-- Logo Tailwind CSS}

\begin{longtable}[]{@{}
  >{\raggedright\arraybackslash}p{(\columnwidth - 0\tabcolsep) * \real{1.0000}}@{}}
\toprule()
\begin{minipage}[b]{\linewidth}\raggedright
\begin{quote}
--- \textbf{Python} {[}11{]} : langage polyvalent utilis´e pour la
logique backend et la d´etection

d'´emotions via des algorithmes d'intelligence artificielle.
\end{quote}
\end{minipage} \\
\midrule()
\endhead
\bottomrule()
\end{longtable}

\begin{longtable}[]{@{}
  >{\raggedright\arraybackslash}p{(\columnwidth - 0\tabcolsep) * \real{1.0000}}@{}}
\toprule()
\begin{minipage}[b]{\linewidth}\raggedright
\includegraphics[width=0.99167in,height=1.08889in]{vertopal_f50a6211e6524672a7ad79ef0154e85e/media/image20.png}
\end{minipage} \\
\midrule()
\endhead
\bottomrule()
\end{longtable}

Figure 2.14 \emph{-- Logo Python}

31

\begin{longtable}[]{@{}
  >{\raggedright\arraybackslash}p{(\columnwidth - 0\tabcolsep) * \real{1.0000}}@{}}
\toprule()
\begin{minipage}[b]{\linewidth}\raggedright
\begin{quote}
Analyse et sp´ecification des besoins
\end{quote}
\end{minipage} \\
\midrule()
\endhead
\begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
--- \textbf{Flask} {[}12{]} : micro-framework Python l´eger permettant
le d´eveloppement rapide d'API
\end{quote}
\end{minipage} \\
\bottomrule()
\end{longtable}

\begin{quote}
et de services web.
\end{quote}

\begin{longtable}[]{@{}
  >{\raggedright\arraybackslash}p{(\columnwidth - 0\tabcolsep) * \real{1.0000}}@{}}
\toprule()
\begin{minipage}[b]{\linewidth}\raggedright
\includegraphics[width=0.99167in,height=0.99167in]{vertopal_f50a6211e6524672a7ad79ef0154e85e/media/image21.png}
\end{minipage} \\
\midrule()
\endhead
\bottomrule()
\end{longtable}

Figure 2.15 \emph{-- Logo Flask}

\begin{longtable}[]{@{}
  >{\raggedright\arraybackslash}p{(\columnwidth - 0\tabcolsep) * \real{1.0000}}@{}}
\toprule()
\begin{minipage}[b]{\linewidth}\raggedright
\begin{quote}
--- \textbf{Jest} {[}13{]} : Jest est un framework de test JavaScript
con¸cu pour assurer la correction de

toute base de code JavaScript. Il vous permet d'´ecrire des tests avec
une API accessible,

famili` ere et riche en fonctionnalit´es qui vous donne des r´esultats
rapidement.
\end{quote}
\end{minipage} \\
\midrule()
\endhead
\bottomrule()
\end{longtable}

\begin{longtable}[]{@{}
  >{\raggedright\arraybackslash}p{(\columnwidth - 0\tabcolsep) * \real{1.0000}}@{}}
\toprule()
\begin{minipage}[b]{\linewidth}\raggedright
\includegraphics[width=0.99167in,height=0.99167in]{vertopal_f50a6211e6524672a7ad79ef0154e85e/media/image22.png}
\end{minipage} \\
\midrule()
\endhead
\bottomrule()
\end{longtable}

Figure 2.16 \emph{-- Logo Jest}

\begin{quote}
\textbf{2.6} \textbf{Conclusion}
\end{quote}

Au cours de la phase de sp´ecification des besoins et d'analyse, notre
objectif ´etait de clarifier

et de simplifier les exigences de nos clients. Ce chapitre d´ebute par
la pr´esentation des besoins

fonctionnels et non fonctionnels de notre syst` eme, suivie par
l'architecture globale, physique

et logique adopt´ee pour le projet. Ensuite, nous avons d´etaill´e les
technologies, frameworks et

\begin{quote}
logiciels s´electionn´es pour le d´eveloppement.
\end{quote}

Le prochain chapitre se concentrera sur l'´etude des premiers sprints,
o` u nous mettrons en

pratique les fondements pos´es ici pour cr´eer et impl´ementer les
fonctionnalit´es essentielles du

\begin{quote}
projet.
\end{quote}

32

\begin{longtable}[]{@{}
  >{\raggedright\arraybackslash}p{(\columnwidth - 2\tabcolsep) * \real{0.5000}}
  >{\raggedright\arraybackslash}p{(\columnwidth - 2\tabcolsep) * \real{0.5000}}@{}}
\toprule()
\begin{minipage}[b]{\linewidth}\raggedright
Chapitre\textbf{3}
\end{minipage} & \begin{minipage}[b]{\linewidth}\raggedright
\textbf{Authentification et param` etres administratifs}
\end{minipage} \\
\midrule()
\endhead
\bottomrule()
\end{longtable}

33

\begin{longtable}[]{@{}
  >{\raggedright\arraybackslash}p{(\columnwidth - 2\tabcolsep) * \real{0.5000}}
  >{\raggedright\arraybackslash}p{(\columnwidth - 2\tabcolsep) * \real{0.5000}}@{}}
\toprule()
\multicolumn{2}{@{}>{\raggedright\arraybackslash}p{(\columnwidth - 2\tabcolsep) * \real{1.0000} + 2\tabcolsep}@{}}{%
\begin{minipage}[b]{\linewidth}\raggedright
\begin{quote}
Authentification et param` etres administratifs
\end{quote}
\end{minipage}} \\
\midrule()
\endhead
\begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
\textbf{3.1}
\end{quote}
\end{minipage} & \begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
\textbf{Introduction}
\end{quote}
\end{minipage} \\
\multicolumn{2}{@{}>{\raggedright\arraybackslash}p{(\columnwidth - 2\tabcolsep) * \real{1.0000} + 2\tabcolsep}@{}}{%
\begin{minipage}[t]{\linewidth}\raggedright
Dans ce chapitre, nous examinons les premi` eres ´etapes concr` etes du
d´eveloppement de la

plateforme Pro-Tracker, en nous concentrant sur les fonctionnalit´es
cl´es garantissant une uti-

lisation simple et s´ecuris´ee de l' outil . Le premier sprint a ´et´e
largement consacr´e ` a la mise

en œuvre des modules d'authentification , de gestion des profils
utilisateurs et des param` etres

d'administration . Ces ´el´ements constituent les bases de la plateforme
, permettant une ges-

tion centralis´ee des acc` es , une configuration des profils bas´ee sur
les rˆoles et une configuration

\begin{quote}
flexible des param` etres de l' application .
\end{quote}
\end{minipage}} \\
\begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
\textbf{3.2}
\end{quote}
\end{minipage} & \begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
\textbf{Backlog du sprint 1}
\end{quote}
\end{minipage} \\
\multicolumn{2}{@{}>{\raggedright\arraybackslash}p{(\columnwidth - 2\tabcolsep) * \real{1.0000} + 2\tabcolsep}@{}}{%
Le Sprint 1 a lanc´e le d´eveloppement de Pro-Tracker en posant les
bases techniques : configura-

tion de l'environnement, outils de versioning, installation des
d´ependances. Les tˆaches, issues

du backlog, ont ´et´e prioris´ees sous forme d'user stories, menant ` a
la cr´eation des premiers

modules cl´es : authentification, gestion des profils, param` etres
administratifs et internationa-} \\
\bottomrule()
\end{longtable}

\begin{quote}
lisation.
\end{quote}

\begin{longtable}[]{@{}
  >{\raggedright\arraybackslash}p{(\columnwidth - 0\tabcolsep) * \real{1.0000}}@{}}
\toprule()
\begin{minipage}[b]{\linewidth}\raggedright
Table 3.1 \emph{-- Backlog de sprint 1-- Authentification, gestion des
profils et param` etres administratifs}

\begin{longtable}[]{@{}
  >{\raggedright\arraybackslash}p{(\columnwidth - 4\tabcolsep) * \real{0.3333}}
  >{\raggedright\arraybackslash}p{(\columnwidth - 4\tabcolsep) * \real{0.3333}}
  >{\raggedright\arraybackslash}p{(\columnwidth - 4\tabcolsep) * \real{0.3333}}@{}}
\toprule()
\begin{minipage}[b]{\linewidth}\raggedright
\begin{quote}
\textbf{Feature}
\end{quote}
\end{minipage} & \begin{minipage}[b]{\linewidth}\raggedright
\begin{quote}
\textbf{User Story}
\end{quote}
\end{minipage} & \begin{minipage}[b]{\linewidth}\raggedright
\begin{quote}
\textbf{Estimation (jours)}
\end{quote}
\end{minipage} \\
\midrule()
\endhead
\begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
Authentification
\end{quote}
\end{minipage} & \begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
- En tant qu'utilisateur, je veux cr´eer un compte\\
- En tant qu'utilisateur, je veux me connecter avec un email et un mot
de passe
\end{quote}\strut
\end{minipage} & \begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
2

2
\end{quote}
\end{minipage} \\
& \begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
- En tant qu'utilisateur, lorsque j'ai oubli´mon mot de passe, je veux
le modifier
\end{quote}

\begin{longtable}[]{@{}
  >{\raggedright\arraybackslash}p{(\columnwidth - 12\tabcolsep) * \real{0.1429}}
  >{\raggedright\arraybackslash}p{(\columnwidth - 12\tabcolsep) * \real{0.1429}}
  >{\raggedright\arraybackslash}p{(\columnwidth - 12\tabcolsep) * \real{0.1429}}
  >{\raggedright\arraybackslash}p{(\columnwidth - 12\tabcolsep) * \real{0.1429}}
  >{\raggedright\arraybackslash}p{(\columnwidth - 12\tabcolsep) * \real{0.1429}}
  >{\raggedright\arraybackslash}p{(\columnwidth - 12\tabcolsep) * \real{0.1429}}
  >{\raggedright\arraybackslash}p{(\columnwidth - 12\tabcolsep) * \real{0.1429}}@{}}
\toprule()
\begin{minipage}[b]{\linewidth}\raggedright
-
\end{minipage} & \begin{minipage}[b]{\linewidth}\raggedright
En
\end{minipage} & \begin{minipage}[b]{\linewidth}\raggedright
tant
\end{minipage} & \begin{minipage}[b]{\linewidth}\raggedright
qu'utilisateur,
\end{minipage} &
\multirow{2}{*}{\begin{minipage}[b]{\linewidth}\raggedright
je
\end{minipage}} &
\multirow{2}{*}{\begin{minipage}[b]{\linewidth}\raggedright
veux
\end{minipage}} &
\multirow{2}{*}{\begin{minipage}[b]{\linewidth}\raggedright
me
\end{minipage}} \\
\multicolumn{4}{@{}>{\raggedright\arraybackslash}p{(\columnwidth - 12\tabcolsep) * \real{0.5714} + 6\tabcolsep}}{%
\begin{minipage}[b]{\linewidth}\raggedright
\begin{quote}
d´econnecter du syst` eme
\end{quote}
\end{minipage}} \\
\midrule()
\endhead
\bottomrule()
\end{longtable}
\end{minipage} & \begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
3

1
\end{quote}
\end{minipage} \\
\begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
Gestion des comptes
\end{quote}
\end{minipage} & \begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
- En tant qu'administrateur, je veux consul-ter la liste des comptes
utilisateurs\\
- En tant qu'administrateur, je veux modifier les informations d'un
compte existant
\end{quote}\strut
\end{minipage} & \begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
2

2
\end{quote}
\end{minipage} \\
\bottomrule()
\end{longtable}\strut
\end{minipage} \\
\midrule()
\endhead
\bottomrule()
\end{longtable}

34

\begin{longtable}[]{@{}
  >{\raggedright\arraybackslash}p{(\columnwidth - 0\tabcolsep) * \real{1.0000}}@{}}
\toprule()
\begin{minipage}[b]{\linewidth}\raggedright
\begin{quote}
Authentification et param` etres administratifs
\end{quote}
\end{minipage} \\
\midrule()
\endhead
\bottomrule()
\end{longtable}

\begin{longtable}[]{@{}
  >{\raggedright\arraybackslash}p{(\columnwidth - 4\tabcolsep) * \real{0.3333}}
  >{\raggedright\arraybackslash}p{(\columnwidth - 4\tabcolsep) * \real{0.3333}}
  >{\raggedright\arraybackslash}p{(\columnwidth - 4\tabcolsep) * \real{0.3333}}@{}}
\toprule()
\begin{minipage}[b]{\linewidth}\raggedright
\end{minipage} & \begin{minipage}[b]{\linewidth}\raggedright
\begin{quote}
- En tant qu'administrateur, je veux inactiver un compte utilisateur
\end{quote}

\begin{longtable}[]{@{}
  >{\raggedright\arraybackslash}p{(\columnwidth - 10\tabcolsep) * \real{0.1667}}
  >{\raggedright\arraybackslash}p{(\columnwidth - 10\tabcolsep) * \real{0.1667}}
  >{\raggedright\arraybackslash}p{(\columnwidth - 10\tabcolsep) * \real{0.1667}}
  >{\raggedright\arraybackslash}p{(\columnwidth - 10\tabcolsep) * \real{0.1667}}
  >{\raggedright\arraybackslash}p{(\columnwidth - 10\tabcolsep) * \real{0.1667}}
  >{\raggedright\arraybackslash}p{(\columnwidth - 10\tabcolsep) * \real{0.1667}}@{}}
\toprule()
\begin{minipage}[b]{\linewidth}\raggedright
-
\end{minipage} & \begin{minipage}[b]{\linewidth}\raggedright
En
\end{minipage} & \begin{minipage}[b]{\linewidth}\raggedright
tant
\end{minipage} & \begin{minipage}[b]{\linewidth}\raggedright
qu'administrateur,
\end{minipage} &
\multirow{2}{*}{\begin{minipage}[b]{\linewidth}\raggedright
je
\end{minipage}} &
\multirow{2}{*}{\begin{minipage}[b]{\linewidth}\raggedright
\begin{quote}
veux
\end{quote}
\end{minipage}} \\
\multicolumn{4}{@{}>{\raggedright\arraybackslash}p{(\columnwidth - 10\tabcolsep) * \real{0.6667} + 6\tabcolsep}}{%
\begin{minipage}[b]{\linewidth}\raggedright
\begin{quote}
R´einitialiser le mot de passe
\end{quote}
\end{minipage}} \\
\midrule()
\endhead
\bottomrule()
\end{longtable}

\begin{quote}
- En tant qu'administrateur, je veux filtrer par roles\\
- En tant qu'administrateur, je veux chercher par nom et d´epartement
\end{quote}\strut
\end{minipage} & \begin{minipage}[b]{\linewidth}\raggedright
\begin{quote}
3

3

2

2
\end{quote}
\end{minipage} \\
\midrule()
\endhead
\begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
Profil
\end{quote}
\end{minipage} & \begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
- En tant qu'utilisateur, je veux consulter et modifier mon profil\\
- En tant qu'utilisateur, je veux changer mon avatar\\
- En tant qu'utilisateur, je veux changer mon mot de passe\\
- En tant qu'utilisateur, je veux consulter les activit´es r´ecentes
\end{quote}\strut
\end{minipage} & \begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
2

2

2

2
\end{quote}
\end{minipage} \\
\begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
horaires de travail
\end{quote}
\end{minipage} & \begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
- En tant qu'administrateur, je veux consul-ter la liste des horaires de
travail\\
- En tant qu'administrateur, je veux enregis-trer une nouvelle horaire
de travail\\
- En tant qu'administrateur, je veux modifier une horaire de travail
existante\\
- En tant qu'administrateur, je veux suppri-mer une horaire de travail
existante
\end{quote}\strut
\end{minipage} & \begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
0.5

1

1

0.5
\end{quote}
\end{minipage} \\
\begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
Invite utilisateur
\end{quote}
\end{minipage} & \begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
- En tant qu'administrateur, je veux invi-ter un nouvel utilisateur ` a
rejoindre la pla-teforme
\end{quote}
\end{minipage} & \begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
4
\end{quote}
\end{minipage} \\
\begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
Rˆoles et Permissions
\end{quote}
\end{minipage} & \begin{minipage}[t]{\linewidth}\raggedright
- En tant qu'administrateur, je veux cr´eer et

\begin{quote}
g´erer les rˆoles attribu´es aux utilisateurs
\end{quote}
\end{minipage} & \begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
2
\end{quote}
\end{minipage} \\
& \begin{minipage}[t]{\linewidth}\raggedright
- En tant qu'administrateur, je veux d´efinir

\begin{quote}
les permissions associ´ees ` a chaque rˆole
\end{quote}

- En tant qu'administrateur, je veux consul-

\begin{quote}
ter les permissions associ´ees ` a chaque rˆole
\end{quote}
\end{minipage} & \begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
2

2
\end{quote}
\end{minipage} \\
\bottomrule()
\end{longtable}

35

\begin{longtable}[]{@{}
  >{\raggedright\arraybackslash}p{(\columnwidth - 0\tabcolsep) * \real{1.0000}}@{}}
\toprule()
\begin{minipage}[b]{\linewidth}\raggedright
\begin{quote}
Authentification et param` etres administratifs
\end{quote}
\end{minipage} \\
\midrule()
\endhead
\bottomrule()
\end{longtable}

\begin{longtable}[]{@{}
  >{\raggedright\arraybackslash}p{(\columnwidth - 4\tabcolsep) * \real{0.3333}}
  >{\raggedright\arraybackslash}p{(\columnwidth - 4\tabcolsep) * \real{0.3333}}
  >{\raggedright\arraybackslash}p{(\columnwidth - 4\tabcolsep) * \real{0.3333}}@{}}
\toprule()
\begin{minipage}[b]{\linewidth}\raggedright
\begin{quote}
Internationalisation
\end{quote}
\end{minipage} & \begin{minipage}[b]{\linewidth}\raggedright
\begin{quote}
- En tant qu'utilisateur, je veux changer la langue de l'application
\end{quote}
\end{minipage} & \begin{minipage}[b]{\linewidth}\raggedright
\begin{quote}
3
\end{quote}
\end{minipage} \\
\midrule()
\endhead
\bottomrule()
\end{longtable}

\begin{longtable}[]{@{}
  >{\raggedright\arraybackslash}p{(\columnwidth - 2\tabcolsep) * \real{0.5000}}
  >{\raggedright\arraybackslash}p{(\columnwidth - 2\tabcolsep) * \real{0.5000}}@{}}
\toprule()
\begin{minipage}[b]{\linewidth}\raggedright
\begin{quote}
\textbf{3.3}
\end{quote}
\end{minipage} & \begin{minipage}[b]{\linewidth}\raggedright
\begin{quote}
\textbf{Sp´ecification fonctionnelle}
\end{quote}
\end{minipage} \\
\midrule()
\endhead
\multicolumn{2}{@{}>{\raggedright\arraybackslash}p{(\columnwidth - 2\tabcolsep) * \real{1.0000} + 2\tabcolsep}@{}}{%
\begin{minipage}[t]{\linewidth}\raggedright
Cette section pr´esente les fonctionalit´es du premier sprint. Nous
introduisons d'abord le dia-

\begin{quote}
gramme de cas d'utilisation, illustrant les actions possibles des
utilisateurs dans le syst` eme.
\end{quote}
\end{minipage}} \\
\textbf{3.3.1} & \begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
\textbf{Diagramme de cas d'utilisation du premier sprint}
\end{quote}
\end{minipage} \\
\multicolumn{2}{@{}>{\raggedright\arraybackslash}p{(\columnwidth - 2\tabcolsep) * \real{1.0000} + 2\tabcolsep}@{}}{%
\begin{minipage}[t]{\linewidth}\raggedright
Le diagramme de cas d'utilisation ci-dessous pr´esente les actions
utilisateur pr´evues pour le

Sprint 1. Les cas en blanc indiquent ceux qui m'ont ´et´e attribu´es,
selon la r´epartition entre

\begin{quote}
d´eveloppeurs.
\end{quote}
\end{minipage}} \\
\bottomrule()
\end{longtable}

\includegraphics[width=6.61111in,height=3.96944in]{vertopal_f50a6211e6524672a7ad79ef0154e85e/media/image23.png}

Figure 3.1 \emph{-- diagramme de cas d'utilisation du premier sprint}

\begin{longtable}[]{@{}
  >{\raggedright\arraybackslash}p{(\columnwidth - 0\tabcolsep) * \real{1.0000}}@{}}
\toprule()
\begin{minipage}[b]{\linewidth}\raggedright
Ce diagramme montre les cas d'utilisation du sprint 1. Tous les cas
n´ecessitent l'authentifica-

\begin{quote}
tion (\emph{≪}s'authentifier\emph{≫}) avant d'ˆetre ex´ecut´es.
\end{quote}
\end{minipage} \\
\midrule()
\endhead
\bottomrule()
\end{longtable}

36

\begin{longtable}[]{@{}
  >{\raggedright\arraybackslash}p{(\columnwidth - 0\tabcolsep) * \real{1.0000}}@{}}
\toprule()
\begin{minipage}[b]{\linewidth}\raggedright
\begin{quote}
Authentification et param` etres administratifs
\end{quote}
\end{minipage} \\
\midrule()
\endhead
\begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
\textbf{Raffinement de cas d'utilisation}\emph{≪}\textbf{G´erer les
horaires de travail}\emph{≫}
\end{quote}

La figure 3.2 illustre le raffinement du cas d'utilisation\emph{≪}G´erer
les horaires de travail\emph{≫}afin
\end{minipage} \\
\bottomrule()
\end{longtable}

\begin{quote}
d'expliquer son aspect fonctionnel.
\end{quote}

\includegraphics[width=5.95in,height=4.5625in]{vertopal_f50a6211e6524672a7ad79ef0154e85e/media/image24.png}

Figure 3.2 \emph{-- Diagramme de cas d'utilisation raffin´e :≪G´erer les
horaires de travail≫}

\begin{quote}
\textbf{3.3.2} \textbf{Description textuelle}

\textbf{Description textuelle de cas d'utilisation gestion des horaires
de travail}

Table 3.2 \emph{-- Description textuelle -- Gestion des horaires de
travail}
\end{quote}

\begin{longtable}[]{@{}
  >{\raggedright\arraybackslash}p{(\columnwidth - 2\tabcolsep) * \real{0.5000}}
  >{\raggedright\arraybackslash}p{(\columnwidth - 2\tabcolsep) * \real{0.5000}}@{}}
\toprule()
\begin{minipage}[b]{\linewidth}\raggedright
\begin{quote}
\textbf{El´´ement}
\end{quote}
\end{minipage} & \begin{minipage}[b]{\linewidth}\raggedright
\begin{quote}
\textbf{Description}
\end{quote}
\end{minipage} \\
\midrule()
\endhead
\begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
Titre
\end{quote}
\end{minipage} & \begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
Gestion des horaires de travail
\end{quote}
\end{minipage} \\
\begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
Acteur
\end{quote}
\end{minipage} & \begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
Administrateur
\end{quote}
\end{minipage} \\
\begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
Pr´e conditions
\end{quote}
\end{minipage} & \begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
L'administrateur est authentifi´e dans le syst` eme
\end{quote}
\end{minipage} \\
\bottomrule()
\end{longtable}

37

\begin{longtable}[]{@{}
  >{\raggedright\arraybackslash}p{(\columnwidth - 0\tabcolsep) * \real{1.0000}}@{}}
\toprule()
\begin{minipage}[b]{\linewidth}\raggedright
\begin{quote}
Authentification et param` etres administratifs
\end{quote}
\end{minipage} \\
\midrule()
\endhead
\bottomrule()
\end{longtable}

\begin{longtable}[]{@{}
  >{\raggedright\arraybackslash}p{(\columnwidth - 2\tabcolsep) * \real{0.5000}}
  >{\raggedright\arraybackslash}p{(\columnwidth - 2\tabcolsep) * \real{0.5000}}@{}}
\toprule()
\begin{minipage}[b]{\linewidth}\raggedright
\begin{quote}
Post conditions
\end{quote}
\end{minipage} & \begin{minipage}[b]{\linewidth}\raggedright
Les horaires de travail sont correctement enregistr´ees,

\begin{quote}
mises ` a jour ou supprim´ees
\end{quote}
\end{minipage} \\
\midrule()
\endhead
\begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
Sc´enario principal
\end{quote}
\end{minipage} & \begin{minipage}[t]{\linewidth}\raggedright
--- L'administrateur acc` ede ` a la section ''horaires de tra-

\begin{quote}
vail''

--- Il consulte la liste de ses horaires enregistr´ees
\end{quote}

--- Il peut ajouter une nouvelle entr´ee en pr´ecisant le

\begin{quote}
titre ,la date de dabut , date fin , l'heure de d´ebut et

de fin , et jours de travail .

--- Il peut modifier une entr´ee existante

--- Il peut supprimer une entr´ee si n´ecessaire
\end{quote}
\end{minipage} \\
\bottomrule()
\end{longtable}

\begin{longtable}[]{@{}
  >{\raggedright\arraybackslash}p{(\columnwidth - 0\tabcolsep) * \real{1.0000}}@{}}
\toprule()
\begin{minipage}[b]{\linewidth}\raggedright
\begin{quote}
\textbf{Raffinement de cas d'utilisation}\emph{≪}\textbf{G´erer
profile}\emph{≫}
\end{quote}

La figure 3.3 illustre le raffinement du cas d'utilisation\emph{≪}G´erer
profil\emph{≫}afin d'expliquer son
\end{minipage} \\
\midrule()
\endhead
\bottomrule()
\end{longtable}

\begin{quote}
aspect fonctionnel.
\end{quote}

Figure 3.3 \emph{-- Diagramme de cas d'utilisation raffin´e :≪G´erer
profil≫}

\begin{quote}
\textbf{Description textuelle de cas
d'utilisation}\emph{≪}\textbf{inviter utilisateurs}\emph{≫}

Table 3.3 \emph{-- Description textuelle -- Inviter utilisateurs}
\end{quote}

\begin{longtable}[]{@{}
  >{\raggedright\arraybackslash}p{(\columnwidth - 2\tabcolsep) * \real{0.5000}}
  >{\raggedright\arraybackslash}p{(\columnwidth - 2\tabcolsep) * \real{0.5000}}@{}}
\toprule()
\begin{minipage}[b]{\linewidth}\raggedright
\begin{quote}
\textbf{El´´ement}
\end{quote}
\end{minipage} & \begin{minipage}[b]{\linewidth}\raggedright
\begin{quote}
\textbf{Description}
\end{quote}
\end{minipage} \\
\midrule()
\endhead
\begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
Titre
\end{quote}
\end{minipage} & \begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
Inviter un utilisateur
\end{quote}
\end{minipage} \\
\begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
Acteur
\end{quote}
\end{minipage} & \begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
Administrateur
\end{quote}
\end{minipage} \\
\begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
Pr´e conditions
\end{quote}
\end{minipage} & \begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
--- L'administrateur est authentifi´\\
--- Le service d'envoi d'emails est op´erationnel
\end{quote}\strut
\end{minipage} \\
\bottomrule()
\end{longtable}

38

\begin{longtable}[]{@{}
  >{\raggedright\arraybackslash}p{(\columnwidth - 0\tabcolsep) * \real{1.0000}}@{}}
\toprule()
\begin{minipage}[b]{\linewidth}\raggedright
\begin{quote}
Authentification et param` etres administratifs
\end{quote}
\end{minipage} \\
\midrule()
\endhead
\bottomrule()
\end{longtable}

\begin{longtable}[]{@{}
  >{\raggedright\arraybackslash}p{(\columnwidth - 2\tabcolsep) * \real{0.5000}}
  >{\raggedright\arraybackslash}p{(\columnwidth - 2\tabcolsep) * \real{0.5000}}@{}}
\toprule()
\begin{minipage}[b]{\linewidth}\raggedright
\begin{quote}
Sc´enario principal
\end{quote}
\end{minipage} & \begin{minipage}[b]{\linewidth}\raggedright
--- L'administrateur clique sur ''Inviter un nouvel utili-

\begin{quote}
sateur''

--- Une modale s'ouvre avec un formulaire contenant :

--- Champs pour le pr´enom et nom

--- Champ pour l'adresse email

--- S´electeur de rˆole

--- Mot de passe temporaire g´en´er´e automatique-

ment

--- L'administrateur remplit les champs obligatoires

--- Le syst` eme valide les informations et :

--- Cr´ee le compte utilisateur

--- Envoie un email contient l'email et un mot de

passe temporaire
\end{quote}

--- La modale se ferme et un message de confirmation

\begin{quote}
s'affiche
\end{quote}
\end{minipage} \\
\midrule()
\endhead
\begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
Post conditions
\end{quote}
\end{minipage} & \begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
--- Le nouvel utilisateur est enregistr´e dans le syst` eme

--- Un email d'invitation est envoy´e (si succ` es)
\end{quote}
\end{minipage} \\
\bottomrule()
\end{longtable}

\begin{longtable}[]{@{}
  >{\raggedright\arraybackslash}p{(\columnwidth - 2\tabcolsep) * \real{0.5000}}
  >{\raggedright\arraybackslash}p{(\columnwidth - 2\tabcolsep) * \real{0.5000}}@{}}
\toprule()
\begin{minipage}[b]{\linewidth}\raggedright
\begin{quote}
\textbf{3.4}
\end{quote}
\end{minipage} & \begin{minipage}[b]{\linewidth}\raggedright
\begin{quote}
\textbf{Conception}
\end{quote}
\end{minipage} \\
\midrule()
\endhead
\multicolumn{2}{@{}>{\raggedright\arraybackslash}p{(\columnwidth - 2\tabcolsep) * \real{1.0000} + 2\tabcolsep}@{}}{%
\begin{minipage}[t]{\linewidth}\raggedright
Dans cette section, nous abordons la conception du premier sprint. Nous
commen¸cons par

pr´esenter le diagramme de cas d'utilisation de ce sprint, offrant une
vue d'ensemble des actions

possibles des utilisateurs dans notre syst` eme.En fournissant une
description textuelle ainsi

\begin{quote}
qu'un sc´enario d´etaill´e des ´etapes ` a suivre.
\end{quote}
\end{minipage}} \\
\bottomrule()
\end{longtable}

39

\begin{longtable}[]{@{}
  >{\raggedright\arraybackslash}p{(\columnwidth - 2\tabcolsep) * \real{0.5000}}
  >{\raggedright\arraybackslash}p{(\columnwidth - 2\tabcolsep) * \real{0.5000}}@{}}
\toprule()
\multicolumn{2}{@{}>{\raggedright\arraybackslash}p{(\columnwidth - 2\tabcolsep) * \real{1.0000} + 2\tabcolsep}@{}}{%
\begin{minipage}[b]{\linewidth}\raggedright
\begin{quote}
Authentification et param` etres administratifs
\end{quote}
\end{minipage}} \\
\midrule()
\endhead
\begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
\textbf{3.4.1}
\end{quote}
\end{minipage} & \begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
\textbf{Diagrammes de s´equences}
\end{quote}
\end{minipage} \\
\multicolumn{2}{@{}>{\raggedright\arraybackslash}p{(\columnwidth - 2\tabcolsep) * \real{1.0000} + 2\tabcolsep}@{}}{%
\begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
\textbf{Diagramme de s´equence de}\emph{≪}\textbf{Gestion des horaires
de travail}\emph{≫}
\end{quote}

Le diagramme suivant dans la figure 3.4 repr´esente l'interaction entre
l'utilisateur et le syst` eme
\end{minipage}} \\
\bottomrule()
\end{longtable}

\begin{quote}
afin de s'authentifier.
\end{quote}

\includegraphics[width=6.61111in,height=6.34028in]{vertopal_f50a6211e6524672a7ad79ef0154e85e/media/image25.png}

\begin{longtable}[]{@{}
  >{\raggedright\arraybackslash}p{(\columnwidth - 0\tabcolsep) * \real{1.0000}}@{}}
\toprule()
\begin{minipage}[b]{\linewidth}\raggedright
Figure 3.4 \emph{-- diagramme de s´equence de≪Gestion des horaires de
travail≫}
\end{minipage} \\
\midrule()
\endhead
\bottomrule()
\end{longtable}

40

\begin{longtable}[]{@{}
  >{\raggedright\arraybackslash}p{(\columnwidth - 4\tabcolsep) * \real{0.3333}}
  >{\raggedright\arraybackslash}p{(\columnwidth - 4\tabcolsep) * \real{0.3333}}
  >{\raggedright\arraybackslash}p{(\columnwidth - 4\tabcolsep) * \real{0.3333}}@{}}
\toprule()
\multicolumn{3}{@{}>{\raggedright\arraybackslash}p{(\columnwidth - 4\tabcolsep) * \real{1.0000} + 4\tabcolsep}@{}}{%
\begin{minipage}[b]{\linewidth}\raggedright
\begin{quote}
Authentification et param` etres administratifs
\end{quote}
\end{minipage}} \\
\midrule()
\endhead
\multicolumn{3}{@{}>{\raggedright\arraybackslash}p{(\columnwidth - 4\tabcolsep) * \real{1.0000} + 4\tabcolsep}@{}}{%
\begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
\textbf{Digramme de s´equence}\emph{≪}\textbf{G´erer profil}\emph{≫}
\end{quote}
\end{minipage}} \\
La figure 3.5 illustre le diagramme de s´equence du cas d'utilisation &
\emph{≪}G´erer profil & \emph{≫}, dans \\
\multicolumn{3}{@{}>{\raggedright\arraybackslash}p{(\columnwidth - 4\tabcolsep) * \real{1.0000} + 4\tabcolsep}@{}}{%
lequel un utilisateur d´ej` a authentifi´e acc` ede ` a l'interface de
gestion de profil pour effectuer} \\
\bottomrule()
\end{longtable}

\begin{quote}
deux actions principales :

--- \textbf{Modifier son avatar} en t´el´eversant une nouvelle image,

--- \textbf{Changer ses informations personnelles} telles que le nom,
l'adresse e-mail, ou le

num´ero de t´el´ephone ...
\end{quote}

\includegraphics[width=6.61111in,height=5.35972in]{vertopal_f50a6211e6524672a7ad79ef0154e85e/media/image26.png}

\begin{longtable}[]{@{}
  >{\raggedright\arraybackslash}p{(\columnwidth - 0\tabcolsep) * \real{1.0000}}@{}}
\toprule()
\begin{minipage}[b]{\linewidth}\raggedright
Figure 3.5 \emph{-- Digramme de s´equence≪G´erer profil≫}
\end{minipage} \\
\midrule()
\endhead
\bottomrule()
\end{longtable}

41

\begin{longtable}[]{@{}
  >{\raggedright\arraybackslash}p{(\columnwidth - 2\tabcolsep) * \real{0.5000}}
  >{\raggedright\arraybackslash}p{(\columnwidth - 2\tabcolsep) * \real{0.5000}}@{}}
\toprule()
\multicolumn{2}{@{}>{\raggedright\arraybackslash}p{(\columnwidth - 2\tabcolsep) * \real{1.0000} + 2\tabcolsep}@{}}{%
\begin{minipage}[b]{\linewidth}\raggedright
\begin{quote}
Authentification et param` etres administratifs
\end{quote}
\end{minipage}} \\
\midrule()
\endhead
\begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
\textbf{3.4.2}
\end{quote}
\end{minipage} & \begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
\textbf{Diagramme de classes du sprint 1}
\end{quote}
\end{minipage} \\
\bottomrule()
\end{longtable}

La figure 3.6 illustre le digramme de classes de sprint 1. Dans ce
sprint nous avons travaill´

principalement sur les classes utilisateur, profile , roles ,
permissions , et horaires de travail .

\includegraphics[width=6.61111in,height=3.63333in]{vertopal_f50a6211e6524672a7ad79ef0154e85e/media/image27.png}

Figure 3.6 \emph{-- Diagramme de classes du sprint 1}

\begin{quote}
\textbf{Description du diagramme de classes}
\end{quote}

Le diagramme de classes ci-dessous mod´elise la structure des donn´ees
de l'application. Il d´efinit

\begin{quote}
les principales entit´es du syst` eme, leurs attributs et les relations
entre elles.
\end{quote}

\textbf{User :} La classe User repr´esente un compte utilisateur. Elle
contient les informations d'au-

\begin{quote}
thentification. Chaque utilisateur poss` ede un \textbf{seul} profil,
li´e par une relation 1--1.
\end{quote}

\textbf{Profile :} La classe Profile regroupe les informations
personnelles d'un utilisateur : nom,

\begin{longtable}[]{@{}
  >{\raggedright\arraybackslash}p{(\columnwidth - 0\tabcolsep) * \real{1.0000}}@{}}
\toprule()
\begin{minipage}[b]{\linewidth}\raggedright
\begin{quote}
pr´enom, avatar, t´el´ephone, d´epartement, co\^{} ut, etc.
\end{quote}

\textbf{Work hours :} Cette classe enregistre les plages horaires de
travail de toute la soci´et´e. \textbf{Recent}

\textbf{activity :} Cette classe permet de suivre les actions r´ecentes
effectu´ees par un utilisateur.

\begin{quote}
Chaque activit´e est associ´ee ` a un profil.
\end{quote}

\textbf{Permissions :} Elle repr´esente les droits d'acc` es d´efinis
dans le syst` eme. Chaque permission

\begin{quote}
poss` ede un nom et un id. Elle peut ˆetre associ´ee ` a un ou plusieurs
rˆoles.
\end{quote}
\end{minipage} \\
\midrule()
\endhead
\bottomrule()
\end{longtable}

42

\begin{longtable}[]{@{}
  >{\raggedright\arraybackslash}p{(\columnwidth - 0\tabcolsep) * \real{1.0000}}@{}}
\toprule()
\begin{minipage}[b]{\linewidth}\raggedright
\begin{quote}
Authentification et param` etres administratifs
\end{quote}
\end{minipage} \\
\midrule()
\endhead
\begin{minipage}[t]{\linewidth}\raggedright
\textbf{Role :} La classe Role d´efinit les rˆoles d'un utilisateur au
sein du syst` eme. Chaque rˆole est

caract´eris´e par un id, un nom et une description. Un profil peut se
voir attribuer un ou

\begin{quote}
plusieurs rˆoles. De plus, chaque rˆole peut poss´eder plusieurs
permissions.
\end{quote}

\textbf{Role \& permissions :} Cette classe associative permet de g´erer
la relation plusieurs-` a-
\end{minipage} \\
\bottomrule()
\end{longtable}

\begin{quote}
plusieurs entre Role et Permissions.

\textbf{3.5} \textbf{R´ealisation}
\end{quote}

Dans cette partie, nous pr´esentons les interfaces utilisateur de notre
plateforme Pro-

tracker r´ealis´ee dans la premiers sprint.Ces captures permettent une
visualisation directe des

\begin{quote}
diff´erentes fonctionnalit´es et des interactions possibles avec
l'application.

\textbf{3.5.1} \textbf{Param` etres administrative}

\textbf{Interface de liste des utilisateurs}
\end{quote}

L'interface de liste des utilisateurs est une page de l'application qui
permet ` a un administrateur

de rechercher et de g´erer les comptes utilisateurs de mani` ere
efficace, comme illustr´e ` a la figure

\begin{quote}
3.7.
\end{quote}

\includegraphics[width=6.61111in,height=2.51944in]{vertopal_f50a6211e6524672a7ad79ef0154e85e/media/image28.png}

Figure 3.7 \emph{-- Interface liste des utilisateurs}

43

\begin{longtable}[]{@{}
  >{\raggedright\arraybackslash}p{(\columnwidth - 0\tabcolsep) * \real{1.0000}}@{}}
\toprule()
\begin{minipage}[b]{\linewidth}\raggedright
\begin{quote}
Authentification et param` etres administratifs
\end{quote}
\end{minipage} \\
\midrule()
\endhead
\bottomrule()
\end{longtable}

\begin{quote}
\textbf{Interface inviter un utilisateur}
\end{quote}

L'interface ''Inviter un utilisateur'' est un formulaire d´edi´e
permettant ` a un administrateur

d'envoyer une invitation ` a un nouvel utilisateur afin qu'il rejoigne
la plateforme. Cette invi-

tation est g´en´eralement envoy´ee par email et contient un lien pour
rejoindre la plateforme,

\begin{quote}
comme illustr´e ` a la figure 3.8.
\end{quote}

\includegraphics[width=3.96667in,height=3.58055in]{vertopal_f50a6211e6524672a7ad79ef0154e85e/media/image29.png}

Figure 3.8 \emph{-- Interface inviter un utilisateur}

\begin{quote}
\textbf{Invitation par email}
\end{quote}

\begin{longtable}[]{@{}
  >{\raggedright\arraybackslash}p{(\columnwidth - 0\tabcolsep) * \real{1.0000}}@{}}
\toprule()
\begin{minipage}[b]{\linewidth}\raggedright
Cette figure montre l'email envoy´e ` a l'utilisateur, contenant un mot
de passe temporaire lui

\begin{quote}
permettant d'acc´eder ` a la plateforme, comme illustr´e ` a la figure
3.9.
\end{quote}
\end{minipage} \\
\midrule()
\endhead
\bottomrule()
\end{longtable}

44

\begin{longtable}[]{@{}
  >{\raggedright\arraybackslash}p{(\columnwidth - 0\tabcolsep) * \real{1.0000}}@{}}
\toprule()
\begin{minipage}[b]{\linewidth}\raggedright
\begin{quote}
Authentification et param` etres administratifs
\end{quote}
\end{minipage} \\
\midrule()
\endhead
\bottomrule()
\end{longtable}

\includegraphics[width=6.61111in,height=2.52917in]{vertopal_f50a6211e6524672a7ad79ef0154e85e/media/image30.png}

Figure 3.9 \emph{-- Invitation par email}

\begin{quote}
\textbf{Interface liste des horaires de travail}
\end{quote}

L'interface des horaires de travail est une section administrative de
l'application qui permet

a l'administrateur de d´efinir et configurer les horaires officiels de
travail pour l'ensemble de la

soci´et´e. Cette configuration peut ˆetre adapt´ee en fonction des
p´eriodes sp´ecifiques de l'ann´ee,

\begin{quote}
telles que l'´et´e, l'hiver, ou le mois de Ramadan , comme illustr´e ` a
la figure 3.16.
\end{quote}

\includegraphics[width=6.61111in,height=2.625in]{vertopal_f50a6211e6524672a7ad79ef0154e85e/media/image31.png}

Figure 3.10 \emph{-- Interface liste des horaires de travail}

\begin{quote}
\textbf{Interfaces ajouter et supprimer une horaire de travail}
\end{quote}

\begin{longtable}[]{@{}
  >{\raggedright\arraybackslash}p{(\columnwidth - 0\tabcolsep) * \real{1.0000}}@{}}
\toprule()
\begin{minipage}[b]{\linewidth}\raggedright
Les interfaces\emph{≪}Ajouter\emph{≫}et\emph{≪}Modifier une horaire de
travail\emph{≫}permettent ` a l'administrateur

de g´erer les plages horaires globales du personnel, comme illustr´e
dans les figures 3.11 et 3.12.
\end{minipage} \\
\midrule()
\endhead
\bottomrule()
\end{longtable}

45

\begin{longtable}[]{@{}
  >{\raggedright\arraybackslash}p{(\columnwidth - 0\tabcolsep) * \real{1.0000}}@{}}
\toprule()
\begin{minipage}[b]{\linewidth}\raggedright
\begin{quote}
Authentification et param` etres administratifs
\end{quote}
\end{minipage} \\
\midrule()
\endhead
\bottomrule()
\end{longtable}

\begin{quote}
\includegraphics[width=2.975in,height=2.73333in]{vertopal_f50a6211e6524672a7ad79ef0154e85e/media/image32.png}

Figure 3.11 \emph{-- Interface ajouter horaires}

\textbf{3.5.2} \textbf{G´erer profile}

\textbf{Interfaces informations sp´ecifiques}

\includegraphics[width=2.975in,height=2.74583in]{vertopal_f50a6211e6524672a7ad79ef0154e85e/media/image33.png}

Figure 3.12 \emph{-- Interface modifier horaires}
\end{quote}

L'utilisateur peut consulter ses informations personnelles, telles que
ses comp´etences, ses ac-

\begin{quote}
tivit´es r´ecentes, et modifier son avatar, comme illustr´e ` a la
figure 3.13.
\end{quote}

\includegraphics[width=6.61111in,height=3.18056in]{vertopal_f50a6211e6524672a7ad79ef0154e85e/media/image34.png}

\begin{longtable}[]{@{}
  >{\raggedright\arraybackslash}p{(\columnwidth - 0\tabcolsep) * \real{1.0000}}@{}}
\toprule()
\begin{minipage}[b]{\linewidth}\raggedright
Figure 3.13 \emph{-- Interface informations sp´ecifiques}
\end{minipage} \\
\midrule()
\endhead
\bottomrule()
\end{longtable}

46

\begin{longtable}[]{@{}
  >{\raggedright\arraybackslash}p{(\columnwidth - 0\tabcolsep) * \real{1.0000}}@{}}
\toprule()
\begin{minipage}[b]{\linewidth}\raggedright
\begin{quote}
Authentification et param` etres administratifs
\end{quote}
\end{minipage} \\
\midrule()
\endhead
\begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
\textbf{Interface modifier informations sp´ecifiques}

L'utilisateur peut modifier ces informations sp´ecifiques , comme
illustr´e ` a la figure 3.14.
\end{quote}
\end{minipage} \\
\bottomrule()
\end{longtable}

\includegraphics[width=6.61111in,height=3.21667in]{vertopal_f50a6211e6524672a7ad79ef0154e85e/media/image35.png}

Figure 3.14 \emph{-- Interface modifier informations sp´ecifiques}

\begin{quote}
\textbf{Interface changer le mot de passe}
\end{quote}

L'interface ''Changer le mot de passe'' permet ` a l'utilisateur de
mettre ` a jour les informations

de s´ecurit´e de son compte afin de renforcer la protection de ses
donn´ees personnelles , comme

\begin{quote}
illustr´e ` a la figure 3.15.
\end{quote}

\includegraphics[width=5.28889in,height=2.54444in]{vertopal_f50a6211e6524672a7ad79ef0154e85e/media/image36.png}

Figure 3.15 \emph{-- Interface Mot de passe oubli´}

47

\begin{longtable}[]{@{}
  >{\raggedright\arraybackslash}p{(\columnwidth - 24\tabcolsep) * \real{0.0769}}
  >{\raggedright\arraybackslash}p{(\columnwidth - 24\tabcolsep) * \real{0.0769}}
  >{\raggedright\arraybackslash}p{(\columnwidth - 24\tabcolsep) * \real{0.0769}}
  >{\raggedright\arraybackslash}p{(\columnwidth - 24\tabcolsep) * \real{0.0769}}
  >{\raggedright\arraybackslash}p{(\columnwidth - 24\tabcolsep) * \real{0.0769}}
  >{\raggedright\arraybackslash}p{(\columnwidth - 24\tabcolsep) * \real{0.0769}}
  >{\raggedright\arraybackslash}p{(\columnwidth - 24\tabcolsep) * \real{0.0769}}
  >{\raggedright\arraybackslash}p{(\columnwidth - 24\tabcolsep) * \real{0.0769}}
  >{\raggedright\arraybackslash}p{(\columnwidth - 24\tabcolsep) * \real{0.0769}}
  >{\raggedright\arraybackslash}p{(\columnwidth - 24\tabcolsep) * \real{0.0769}}
  >{\raggedright\arraybackslash}p{(\columnwidth - 24\tabcolsep) * \real{0.0769}}
  >{\raggedright\arraybackslash}p{(\columnwidth - 24\tabcolsep) * \real{0.0769}}
  >{\raggedright\arraybackslash}p{(\columnwidth - 24\tabcolsep) * \real{0.0769}}@{}}
\toprule()
\multicolumn{13}{@{}>{\raggedright\arraybackslash}p{(\columnwidth - 24\tabcolsep) * \real{1.0000} + 24\tabcolsep}@{}}{%
\begin{minipage}[b]{\linewidth}\raggedright
\begin{quote}
Authentification et param` etres administratifs
\end{quote}
\end{minipage}} \\
\midrule()
\endhead
\multicolumn{2}{@{}>{\raggedright\arraybackslash}p{(\columnwidth - 24\tabcolsep) * \real{0.1538} + 2\tabcolsep}}{%
\begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
\textbf{3.6}
\end{quote}
\end{minipage}} &
\multicolumn{11}{>{\raggedright\arraybackslash}p{(\columnwidth - 24\tabcolsep) * \real{0.8462} + 20\tabcolsep}@{}}{%
\begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
\textbf{Test et validation}
\end{quote}
\end{minipage}} \\
\multicolumn{2}{@{}>{\raggedright\arraybackslash}p{(\columnwidth - 24\tabcolsep) * \real{0.1538} + 2\tabcolsep}}{%
\begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
\textbf{3.6.1}
\end{quote}
\end{minipage}} &
\multicolumn{11}{>{\raggedright\arraybackslash}p{(\columnwidth - 24\tabcolsep) * \real{0.8462} + 20\tabcolsep}@{}}{%
\begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
\textbf{Test unitaire}
\end{quote}
\end{minipage}} \\
\multicolumn{13}{@{}>{\raggedright\arraybackslash}p{(\columnwidth - 24\tabcolsep) * \real{1.0000} + 24\tabcolsep}@{}}{%
Le test unitaire consiste ` a valider le bon fonctionnement des plus
petites unit´es d'un pro-

gramme, comme les fonctions ou m´ethodes, en les testant de mani` ere
isol´ee. Il permet d'iden-

tifier rapidement les erreurs et contribue ` a rendre le code plus
fiable et plus facile ` a maintenir.} \\
\multicolumn{2}{@{}>{\raggedright\arraybackslash}p{(\columnwidth - 24\tabcolsep) * \real{0.1538} + 2\tabcolsep}}{%
\begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
\textbf{3.6.2}
\end{quote}
\end{minipage}} &
\multicolumn{11}{>{\raggedright\arraybackslash}p{(\columnwidth - 24\tabcolsep) * \real{0.8462} + 20\tabcolsep}@{}}{%
\begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
\textbf{Tests unitaires avec Jest}
\end{quote}
\end{minipage}} \\
\begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
La
\end{quote}
\end{minipage} &
\multicolumn{2}{>{\raggedright\arraybackslash}p{(\columnwidth - 24\tabcolsep) * \real{0.1538} + 2\tabcolsep}}{%
figure} & ci-dessous & illustre & les & r´esultats & des & tests &
r´ealis´es & sur & le & \begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
composant
\end{quote}
\end{minipage} \\
\bottomrule()
\end{longtable}

\begin{quote}
WorkHours(Horaires de travail) avec le framework Jest.
\end{quote}

\begin{longtable}[]{@{}
  >{\raggedright\arraybackslash}p{(\columnwidth - 0\tabcolsep) * \real{1.0000}}@{}}
\toprule()
\begin{minipage}[b]{\linewidth}\raggedright
\includegraphics[width=6.61111in,height=2.38333in]{vertopal_f50a6211e6524672a7ad79ef0154e85e/media/image37.png}
\end{minipage} \\
\midrule()
\endhead
\bottomrule()
\end{longtable}

Figure 3.16 \emph{-- Tests du composant WorkHours}

\begin{longtable}[]{@{}
  >{\raggedright\arraybackslash}p{(\columnwidth - 2\tabcolsep) * \real{0.5000}}
  >{\raggedright\arraybackslash}p{(\columnwidth - 2\tabcolsep) * \real{0.5000}}@{}}
\toprule()
\multicolumn{2}{@{}>{\raggedright\arraybackslash}p{(\columnwidth - 2\tabcolsep) * \real{1.0000} + 2\tabcolsep}@{}}{%
\begin{minipage}[b]{\linewidth}\raggedright
Ce test valide les fonctionnalit´es cl´es du composant WorkHours :
affichage, ajout, modification

\begin{quote}
et mise ` a jour des horaires de travail.
\end{quote}
\end{minipage}} \\
\midrule()
\endhead
\begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
\textbf{3.7}
\end{quote}
\end{minipage} & \begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
\textbf{Conclusion}
\end{quote}
\end{minipage} \\
\multicolumn{2}{@{}>{\raggedright\arraybackslash}p{(\columnwidth - 2\tabcolsep) * \real{1.0000} + 2\tabcolsep}@{}}{%
\begin{minipage}[t]{\linewidth}\raggedright
Ce chapitre a pr´esent´e les fonctionnalit´es cl´es d´evelopp´ees lors
du premier sprint, notamment

l'authentification s´ecuris´ee des utilisateurs, la gestion des param`
etres administratifs et la ges-

tion du profil utilisateur. Ces ´el´ements assurent un contrˆole
efficace, une s´ecurit´e renforc´ee et

\begin{quote}
une personnalisation adapt´ee, posant ainsi les bases solides pour la
suite du projet.
\end{quote}
\end{minipage}} \\
\bottomrule()
\end{longtable}

48

\begin{longtable}[]{@{}
  >{\raggedright\arraybackslash}p{(\columnwidth - 2\tabcolsep) * \real{0.5000}}
  >{\raggedright\arraybackslash}p{(\columnwidth - 2\tabcolsep) * \real{0.5000}}@{}}
\toprule()
\begin{minipage}[b]{\linewidth}\raggedright
Chapitre\textbf{4}
\end{minipage} & \begin{minipage}[b]{\linewidth}\raggedright
\begin{quote}
\textbf{Gestion op´erationnelle et financi` ere des projets}
\end{quote}
\end{minipage} \\
\midrule()
\endhead
\bottomrule()
\end{longtable}

\begin{quote}
\textbf{dans Pro-Tracker}
\end{quote}

49

\begin{longtable}[]{@{}
  >{\raggedright\arraybackslash}p{(\columnwidth - 2\tabcolsep) * \real{0.5000}}
  >{\raggedright\arraybackslash}p{(\columnwidth - 2\tabcolsep) * \real{0.5000}}@{}}
\toprule()
\multicolumn{2}{@{}>{\raggedright\arraybackslash}p{(\columnwidth - 2\tabcolsep) * \real{1.0000} + 2\tabcolsep}@{}}{%
\begin{minipage}[b]{\linewidth}\raggedright
\begin{quote}
Gestion op´erationnelle et financi` ere des projets dans Pro-Tracker
\end{quote}
\end{minipage}} \\
\midrule()
\endhead
\begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
\textbf{4.1}
\end{quote}
\end{minipage} & \begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
\textbf{Introduction}
\end{quote}
\end{minipage} \\
\multicolumn{2}{@{}>{\raggedright\arraybackslash}p{(\columnwidth - 2\tabcolsep) * \real{1.0000} + 2\tabcolsep}@{}}{%
\begin{minipage}[t]{\linewidth}\raggedright
Ce chapitre pr´esente le deuxi` eme sprint de d´eveloppement de la
plateforme Pro-Tracker, ax´

sur l'int´egration des fonctionnalit´es li´ees ` a la gestion
op´erationnelle et financi` ere des projets.

L'objectif principal de ce sprint est de fournir aux utilisateurs une
vue claire et structur´ee de

l'ensemble des projets en cours, d'acc´eder facilement aux tˆaches
associ´ees, de suivre les feuilles

de temps, ainsi que de piloter les aspects financiers de mani` ere
efficace. Grˆace ` a ces modules,

Pro-Tracker permet une meilleure coordination des ´equipes, une
optimisation de la gestion

des ressources et une prise de d´ecision plus ´eclair´ee, contribuant
ainsi ` a l'atteinte des objectifs

\begin{quote}
strat´egiques des organisations.
\end{quote}
\end{minipage}} \\
\begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
\textbf{4.2}
\end{quote}
\end{minipage} & \begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
\textbf{Backlog du sprint 2}
\end{quote}
\end{minipage} \\
\multicolumn{2}{@{}>{\raggedright\arraybackslash}p{(\columnwidth - 2\tabcolsep) * \real{1.0000} + 2\tabcolsep}@{}}{%
\begin{minipage}[t]{\linewidth}\raggedright
Le backlog du Sprint 2 regroupe l'ensemble des fonctionnalit´es ` a
d´evelopper en lien avec la

gestion op´erationnelle et financi` ere des projets sur la plateforme
Pro-Tracker. Il comprend no-

tamment l'impl´ementation des interfaces de consultation des projets et
des tˆaches, l'int´egration

d'un module de pilotage financier permettant de suivre les budgets et
d´epenses, ainsi que la

mise en place d'un syst` eme de consultation des feuilles de temps
soumises par les membres

de l'´equipe. Ce sprint inclut ´egalement la gestion des op´erations
courantes li´ees aux projets,

telles que la mise ` a jour des statuts, l'affectation des tˆaches et le
suivi de l'avancement. Chaque

fonctionnalit´e a ´et´e prioris´ee en fonction de sa valeur ajout´ee
pour l'utilisateur final et de sa

\begin{quote}
faisabilit´e technique, afin d'assurer une livraison coh´erente et
efficiente.
\end{quote}

Table 4.1 \emph{-- Backlog de sprint 2- Gestion op´erationnelle et
financi` ere des projets dans Pro-Tracker}
\end{minipage}} \\
\bottomrule()
\end{longtable}

\begin{longtable}[]{@{}
  >{\raggedright\arraybackslash}p{(\columnwidth - 4\tabcolsep) * \real{0.3333}}
  >{\raggedright\arraybackslash}p{(\columnwidth - 4\tabcolsep) * \real{0.3333}}
  >{\raggedright\arraybackslash}p{(\columnwidth - 4\tabcolsep) * \real{0.3333}}@{}}
\toprule()
\begin{minipage}[b]{\linewidth}\raggedright
\begin{quote}
\textbf{Feature}
\end{quote}
\end{minipage} & \begin{minipage}[b]{\linewidth}\raggedright
\begin{quote}
\textbf{User Story}
\end{quote}
\end{minipage} & \begin{minipage}[b]{\linewidth}\raggedright
\begin{quote}
\textbf{Estimation (jours)}
\end{quote}
\end{minipage} \\
\midrule()
\endhead
\textbf{Analyser les pro-jets et les tˆaches} &
\begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
- En tant qu'administrateur, je veux acc´eder ` la liste compl` ete des
projets disponibles sur la plateforme.

- En tant qu'administrateur, je veux rechercher un projet par mot-cl´e
pour le retrouver rapide-ment.
\end{quote}
\end{minipage} & \begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
1

1
\end{quote}
\end{minipage} \\
\bottomrule()
\end{longtable}

50

\begin{longtable}[]{@{}
  >{\raggedright\arraybackslash}p{(\columnwidth - 0\tabcolsep) * \real{1.0000}}@{}}
\toprule()
\begin{minipage}[b]{\linewidth}\raggedright
\begin{quote}
Gestion op´erationnelle et financi` ere des projets dans Pro-Tracker
\end{quote}
\end{minipage} \\
\midrule()
\endhead
\bottomrule()
\end{longtable}

\begin{longtable}[]{@{}
  >{\raggedright\arraybackslash}p{(\columnwidth - 4\tabcolsep) * \real{0.3333}}
  >{\raggedright\arraybackslash}p{(\columnwidth - 4\tabcolsep) * \real{0.3333}}
  >{\raggedright\arraybackslash}p{(\columnwidth - 4\tabcolsep) * \real{0.3333}}@{}}
\toprule()
\begin{minipage}[b]{\linewidth}\raggedright
\end{minipage} & \begin{minipage}[b]{\linewidth}\raggedright
\begin{quote}
- En tant qu'administrateur, je veux filtrer les projets par statut, et
priorit´e pour faciliter leur consultation.

- En tant qu'administrateur, je veux consulter le tableau de bord d'un
projet pour visualiser ses indicateurs cl´es.

- En tant qu'administrateur, je veux visualiser les membres affect´es `
a un projet et leurs nombre de tˆaches.

- En tant qu'administrateur, je veux consulter aches d'un projet pour
suivre leur la liste des tˆ\\
avancement.

- En tant qu'administrateur, je veux consulter les d´etails d'une tˆache
.

- En tant qu'administrateur, je veux visualiser e associ´es au projet.
les indicateurs de qualit´\\
- En tant qu'administrateur, je veux consulter les details du bug d'une
tˆache.

-En tant qu'administrateur, je souhaite g´erer les ecifique afin
fichiers et les dossiers d'un projet sp´\\
d'organiser et de maintenir ses ressources.
\end{quote}\strut
\end{minipage} & \begin{minipage}[b]{\linewidth}\raggedright
\begin{quote}
1

3

2

1

1

1

1

3
\end{quote}
\end{minipage} \\
\midrule()
\endhead
\begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
\textbf{les finances de la soci´et´}
\end{quote}
\end{minipage} & \begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
- En tant qu'administrateur, je veux ajouter un client au plateforme.

- En tant qu'administrateur, je veux modifier un client a mon
plateforme.
\end{quote}
\end{minipage} & \begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
2

1
\end{quote}
\end{minipage} \\
& \begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
- En tant qu'administrateur, je veux supprimer un client a mon
plateforme.

- En tant qu'administrateur, je veux t´el´echarger un CSV.

- En tant qu'administrateur, je veux ajouter une devis.

- En tant qu'administrateur, je veux t´el´echarger une devis.
\end{quote}
\end{minipage} & \begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
1

2

1

1
\end{quote}
\end{minipage} \\
\bottomrule()
\end{longtable}

51

\begin{longtable}[]{@{}
  >{\raggedright\arraybackslash}p{(\columnwidth - 0\tabcolsep) * \real{1.0000}}@{}}
\toprule()
\begin{minipage}[b]{\linewidth}\raggedright
\begin{quote}
Gestion op´erationnelle et financi` ere des projets dans Pro-Tracker
\end{quote}
\end{minipage} \\
\midrule()
\endhead
\bottomrule()
\end{longtable}

\begin{longtable}[]{@{}
  >{\raggedright\arraybackslash}p{(\columnwidth - 4\tabcolsep) * \real{0.3333}}
  >{\raggedright\arraybackslash}p{(\columnwidth - 4\tabcolsep) * \real{0.3333}}
  >{\raggedright\arraybackslash}p{(\columnwidth - 4\tabcolsep) * \real{0.3333}}@{}}
\toprule()
\begin{minipage}[b]{\linewidth}\raggedright
\end{minipage} & \begin{minipage}[b]{\linewidth}\raggedright
\begin{quote}
- En tant qu'administrateur, je veux consulter la liste des facture.

- En tant qu'administrateur, je veux consulter les paiement\\
- En tant qu'administrateur, je veux modifier les paiement\\
- En tant qu'administrateur, je veux supprimer les paiement\\
- En tant qu'administrateur, je veux ajouter des d´epenses (frais).

- En tant qu'administrateur, je veux modifier des d´epenses (frais).

- En tant qu'administrateur, je veux supprimer epenses (frais). des d´\\
- En tant qu'administrateur, je veux t´el´echarger un CSV des d´epenses
(frais).

- En tant qu'administrateur, je veux consulter le ecifique aux finance
de la soci´et´e. dashboard sp´
\end{quote}\strut
\end{minipage} & \begin{minipage}[b]{\linewidth}\raggedright
\begin{quote}
1

1

1

1

2

1

1

2

3
\end{quote}
\end{minipage} \\
\midrule()
\endhead
\textbf{Feuille de temps} & \begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
En tant qu'administrateur, je veux consulter les feuilles de temps pour
chaque member.

En tant qu'administrateur, je veux filtrer par member , date , type et
projet.
\end{quote}

En tant qu'administrateur, je veux exporter les feuilles de temps sous
forme de PDF ou Excel.
\end{minipage} & \begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
1

2

2
\end{quote}
\end{minipage} \\
\begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
\textbf{Operations}
\end{quote}
\end{minipage} & \begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
En tant qu'administrateur, je veux ajouter une operation et l'affecter
aux membres de l'´equipe. En tant qu'administrateur, je veux modifier
une operation.

En tant qu'administrateur, je veux supprimer une op´eration.
\end{quote}
\end{minipage} & \begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
2

1

1
\end{quote}
\end{minipage} \\
\bottomrule()
\end{longtable}

52

\begin{longtable}[]{@{}
  >{\raggedright\arraybackslash}p{(\columnwidth - 2\tabcolsep) * \real{0.5000}}
  >{\raggedright\arraybackslash}p{(\columnwidth - 2\tabcolsep) * \real{0.5000}}@{}}
\toprule()
\multicolumn{2}{@{}>{\raggedright\arraybackslash}p{(\columnwidth - 2\tabcolsep) * \real{1.0000} + 2\tabcolsep}@{}}{%
\begin{minipage}[b]{\linewidth}\raggedright
\begin{quote}
Gestion op´erationnelle et financi` ere des projets dans Pro-Tracker
\end{quote}
\end{minipage}} \\
\midrule()
\endhead
\begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
\textbf{4.3}
\end{quote}
\end{minipage} & \begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
\textbf{Sp´ecification fonctionnelle}
\end{quote}
\end{minipage} \\
\multicolumn{2}{@{}>{\raggedright\arraybackslash}p{(\columnwidth - 2\tabcolsep) * \real{1.0000} + 2\tabcolsep}@{}}{%
Cette section pr´esente les fonctionnalit´es du deuxi` eme sprint. Nous
introduisons d'abord le

diagramme de cas d'utilisation, illustrant les actions possibles des
utilisateurs dans le syst` eme.} \\
\begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
\textbf{4.3.1}
\end{quote}
\end{minipage} & \begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
\textbf{Diagramme de cas d'utilisation du deuxi´eme sprint}
\end{quote}
\end{minipage} \\
\multicolumn{2}{@{}>{\raggedright\arraybackslash}p{(\columnwidth - 2\tabcolsep) * \real{1.0000} + 2\tabcolsep}@{}}{%
\begin{minipage}[t]{\linewidth}\raggedright
Le diagramme de cas d'utilisation illustre les actions possibles des
utilisateurs pour le deuxi` eme

sprint, regroupant toutes les fonctionnalit´es pr´evues. Les cas
d'utilisation qui me sont attribu´es,

\begin{quote}
selon la r´epartition entre d´eveloppeurs, sont en blanc.
\end{quote}
\end{minipage}} \\
\bottomrule()
\end{longtable}

\includegraphics[width=6.61111in,height=4.51528in]{vertopal_f50a6211e6524672a7ad79ef0154e85e/media/image38.png}

\begin{longtable}[]{@{}
  >{\raggedright\arraybackslash}p{(\columnwidth - 0\tabcolsep) * \real{1.0000}}@{}}
\toprule()
\begin{minipage}[b]{\linewidth}\raggedright
Figure 4.1 \emph{-- diagramme de cas d'utilisation du deuxi´eme sprint}

Ce diagramme illustre les fonctionnalit´es disponibles pour
l'administrateur dans le sprint 2.

Chaque action (comme consulter la liste des projets, consulter les
´equipes des projets) n´ecessite

une authentification pr´ealable. Il met en ´evidence les interactions
possibles li´ees ` a la gestion

\begin{quote}
des projets, des op´erations et des finances.
\end{quote}
\end{minipage} \\
\midrule()
\endhead
\bottomrule()
\end{longtable}

53

\begin{longtable}[]{@{}
  >{\raggedright\arraybackslash}p{(\columnwidth - 0\tabcolsep) * \real{1.0000}}@{}}
\toprule()
\begin{minipage}[b]{\linewidth}\raggedright
\begin{quote}
Gestion op´erationnelle et financi` ere des projets dans Pro-Tracker
\end{quote}
\end{minipage} \\
\midrule()
\endhead
\begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
\textbf{Raffinement de cas d'utilisation}\emph{≪}\textbf{G´erer les
fichiers d'un projet}\emph{≫}
\end{quote}

La figure 4.2 illustre le raffinement du cas d'utilisation\emph{≪}G´erer
les fichiers d'un projet\emph{≫}afin
\end{minipage} \\
\bottomrule()
\end{longtable}

\begin{quote}
d'expliquer son aspect fonctionnel.
\end{quote}

\includegraphics[width=6.61111in,height=5.11806in]{vertopal_f50a6211e6524672a7ad79ef0154e85e/media/image39.png}

Figure 4.2 \emph{-- Diagramme de cas d'utilisation raffin´e :≪G´erer les
fichiers d'un projet≫}

\begin{quote}
\textbf{4.3.2} \textbf{Description textuelle}

\textbf{Description textuelle de cas d'utilisation gestion fichiers d'un
projet}
\end{quote}

\begin{longtable}[]{@{}
  >{\raggedright\arraybackslash}p{(\columnwidth - 0\tabcolsep) * \real{1.0000}}@{}}
\toprule()
\begin{minipage}[b]{\linewidth}\raggedright
Table 4.2 \emph{-- Description textuelle -- G´erer les fichiers d'un
projet}

\begin{longtable}[]{@{}
  >{\raggedright\arraybackslash}p{(\columnwidth - 2\tabcolsep) * \real{0.5000}}
  >{\raggedright\arraybackslash}p{(\columnwidth - 2\tabcolsep) * \real{0.5000}}@{}}
\toprule()
\begin{minipage}[b]{\linewidth}\raggedright
\begin{quote}
\textbf{El´´ement}
\end{quote}
\end{minipage} & \begin{minipage}[b]{\linewidth}\raggedright
\begin{quote}
\textbf{Description}
\end{quote}
\end{minipage} \\
\midrule()
\endhead
\begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
Titre
\end{quote}
\end{minipage} & \begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
G´erer les fichiers d'un projet
\end{quote}
\end{minipage} \\
\bottomrule()
\end{longtable}
\end{minipage} \\
\midrule()
\endhead
\bottomrule()
\end{longtable}

54

\begin{longtable}[]{@{}
  >{\raggedright\arraybackslash}p{(\columnwidth - 0\tabcolsep) * \real{1.0000}}@{}}
\toprule()
\begin{minipage}[b]{\linewidth}\raggedright
\begin{quote}
Gestion op´erationnelle et financi` ere des projets dans Pro-Tracker
\end{quote}
\end{minipage} \\
\midrule()
\endhead
\bottomrule()
\end{longtable}

\begin{longtable}[]{@{}
  >{\raggedright\arraybackslash}p{(\columnwidth - 2\tabcolsep) * \real{0.5000}}
  >{\raggedright\arraybackslash}p{(\columnwidth - 2\tabcolsep) * \real{0.5000}}@{}}
\toprule()
\begin{minipage}[b]{\linewidth}\raggedright
\begin{quote}
\textbf{El´´ement}
\end{quote}
\end{minipage} & \begin{minipage}[b]{\linewidth}\raggedright
\begin{quote}
\textbf{Description}
\end{quote}
\end{minipage} \\
\midrule()
\endhead
\begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
Acteur
\end{quote}
\end{minipage} & \begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
Administrateur
\end{quote}
\end{minipage} \\
\begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
Pr´e conditions
\end{quote}
\end{minipage} & \begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
--- L'administrateur est authentifi´e.
\end{quote}
\end{minipage} \\
\begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
Sc´enario nominal
\end{quote}
\end{minipage} & \begin{minipage}[t]{\linewidth}\raggedright
--- L'administrateur acc` ede ` a l'interface de gestion des

\begin{quote}
fichiers du projet.
\end{quote}

--- Le syst` eme affiche la structure hi´erarchique des dos-

\begin{quote}
siers et fichiers existants.
\end{quote}

--- L'administrateur peut effectuer les actions sui-

\begin{quote}
vantes :

--- Ajouter un nouveau dossier.

--- Ajouter un fichier dans un dossier existant ou `

la racine.

--- Supprimer un fichier ou un dossier.

--- Renommer un fichier ou un dossier.

--- T´el´echarger un fichier.
\end{quote}
\end{minipage} \\
\begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
Post conditions
\end{quote}
\end{minipage} & \begin{minipage}[t]{\linewidth}\raggedright
--- Les fichiers et dossiers sont mis ` a jour selon les ac-

\begin{quote}
tions effectu´ees.

--- La structure du projet est actualis´ee.
\end{quote}
\end{minipage} \\
\begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
Exceptions
\end{quote}
\end{minipage} & \begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
--- Erreur lors du t´el´echargement du fichier.
\end{quote}

--- ´Echec de la cr´eation d'un dossier (conflit de nom,

\begin{quote}
erreur serveur).

--- D´eplacement impossible si cible non valide.
\end{quote}
\end{minipage} \\
\bottomrule()
\end{longtable}

\begin{quote}
\textbf{Description textuelle de cas
d'utilisation}\emph{≪}\textbf{Consulter la liste des projets}\emph{≫}
\end{quote}

55

\begin{longtable}[]{@{}
  >{\raggedright\arraybackslash}p{(\columnwidth - 0\tabcolsep) * \real{1.0000}}@{}}
\toprule()
\begin{minipage}[b]{\linewidth}\raggedright
\begin{quote}
Gestion op´erationnelle et financi` ere des projets dans Pro-Tracker
\end{quote}
\end{minipage} \\
\midrule()
\endhead
\bottomrule()
\end{longtable}

\begin{quote}
Table 4.3 \emph{-- Description textuelle -- Consulter la liste des
projets}
\end{quote}

\begin{longtable}[]{@{}
  >{\raggedright\arraybackslash}p{(\columnwidth - 2\tabcolsep) * \real{0.5000}}
  >{\raggedright\arraybackslash}p{(\columnwidth - 2\tabcolsep) * \real{0.5000}}@{}}
\toprule()
\begin{minipage}[b]{\linewidth}\raggedright
\begin{quote}
\textbf{El´´ement}
\end{quote}
\end{minipage} & \begin{minipage}[b]{\linewidth}\raggedright
\begin{quote}
\textbf{Description}
\end{quote}
\end{minipage} \\
\midrule()
\endhead
\begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
Titre
\end{quote}
\end{minipage} & \begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
Consulter la liste des projets
\end{quote}
\end{minipage} \\
\begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
Acteur
\end{quote}
\end{minipage} & \begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
Administrateur
\end{quote}
\end{minipage} \\
\begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
Pr´e conditions
\end{quote}
\end{minipage} & \begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
--- L'administrateur est connect´
\end{quote}
\end{minipage} \\
\begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
Sc´enario nominal
\end{quote}
\end{minipage} & \begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
--- L'administrateur acc` ede ` a la page des projets

--- Le syst` eme charge les projets depuis l'API

--- L'interface affiche :

--- Un tableau listant les projets avec colonnes :

Nom, Budget, Date de fin, Progression, Prio-

rit´e, Statut ..

--- Une barre de recherche

--- Des filtres d´eroulants (priorit´e, statut)

--- L'administrateur peut :

--- Rechercher par texte (nom/description)

--- Filtrer par priorit´e (HIGH/MEDIUM/LOW)

--- Filtrer par statut (ACTIF/EN RETARD/-

TERMIN´E)

--- Trier les colonnes (clic sur en-tˆete)
\end{quote}

--- En cliquant sur une ligne, l'administrateur acc` ede au

\begin{quote}
d´etail du projet
\end{quote}
\end{minipage} \\
\begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
Post conditions
\end{quote}
\end{minipage} & \begin{minipage}[t]{\linewidth}\raggedright
--- La liste des projets est affich´ee selon les crit` eres

\begin{quote}
s´electionn´es

--- Les filtres/recherches sont appliqu´es

--- L'utilisateur peut acc´eder aux d´etails d'un projet
\end{quote}
\end{minipage} \\
\bottomrule()
\end{longtable}

56

\begin{longtable}[]{@{}
  >{\raggedright\arraybackslash}p{(\columnwidth - 0\tabcolsep) * \real{1.0000}}@{}}
\toprule()
\begin{minipage}[b]{\linewidth}\raggedright
\begin{quote}
Gestion op´erationnelle et financi` ere des projets dans Pro-Tracker
\end{quote}
\end{minipage} \\
\midrule()
\endhead
\begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
\textbf{Description textuelle de cas
d'utilisation}\emph{≪}\textbf{Consulter les ´equipes d'un
projet}\emph{≫}

Table 4.4 \emph{-- Description textuelle -- Consulter les ´equipes d'un
projet}
\end{quote}

\begin{longtable}[]{@{}
  >{\raggedright\arraybackslash}p{(\columnwidth - 2\tabcolsep) * \real{0.5000}}
  >{\raggedright\arraybackslash}p{(\columnwidth - 2\tabcolsep) * \real{0.5000}}@{}}
\toprule()
\begin{minipage}[b]{\linewidth}\raggedright
\begin{quote}
\textbf{El´´ement}
\end{quote}
\end{minipage} & \begin{minipage}[b]{\linewidth}\raggedright
\begin{quote}
\textbf{Description}
\end{quote}
\end{minipage} \\
\midrule()
\endhead
\begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
\textbf{Titre}
\end{quote}
\end{minipage} & \begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
Consulter l'´equipe projet
\end{quote}
\end{minipage} \\
\begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
\textbf{Acteur}
\end{quote}
\end{minipage} & \begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
Administrateur
\end{quote}
\end{minipage} \\
\begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
\textbf{Pr´e conditions}
\end{quote}
\end{minipage} & \begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
--- L'administrateur est connect´e ` a la plateforme.
\end{quote}
\end{minipage} \\
\begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
\textbf{Post conditions}
\end{quote}
\end{minipage} & \begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
--- L'administrateur visualise l'ensemble de l'´equipe

projet.

--- Les tˆaches sont synth´etis´ees par membre.

--- L'administrateur peut consulter les d´etails des

membres et leur activit´e.
\end{quote}
\end{minipage} \\
\begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
\textbf{Sc´enario nominal}
\end{quote}
\end{minipage} & \begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
--- L'administrateur acc` ede ` a la page d'un projet.

--- Le syst` eme r´ecup` ere les profils (membres) associ´es

au projet depuis l'API.

--- Pour chaque membre, le syst` eme r´ecup` ere :
\end{quote}

--- Son nom et pr´enom , son avatar , son rˆole

\begin{quote}
dans le projet ,son d´epartement et son statut.

--- Le syst` eme r´ecup` ere ensuite les tˆaches li´ees au

projet.
\end{quote}

--- Pour chaque membre, les tˆaches sont comptabilis´ees

\begin{quote}
par statut :

--- Termin´ees ,En cours , En attente.

--- L'interface affiche un tableau r´ecapitulatif :

--- Nom, avatar, rˆole, d´epartement, statut ;

--- Tˆaches group´ees par statut avec ´etiquettes

color´ees.
\end{quote}
\end{minipage} \\
\bottomrule()
\end{longtable}
\end{minipage} \\
\bottomrule()
\end{longtable}

57

\begin{longtable}[]{@{}
  >{\raggedright\arraybackslash}p{(\columnwidth - 2\tabcolsep) * \real{0.5000}}
  >{\raggedright\arraybackslash}p{(\columnwidth - 2\tabcolsep) * \real{0.5000}}@{}}
\toprule()
\multicolumn{2}{@{}>{\raggedright\arraybackslash}p{(\columnwidth - 2\tabcolsep) * \real{1.0000} + 2\tabcolsep}@{}}{%
\begin{minipage}[b]{\linewidth}\raggedright
\begin{quote}
Gestion op´erationnelle et financi` ere des projets dans Pro-Tracker
\end{quote}
\end{minipage}} \\
\midrule()
\endhead
\begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
\textbf{4.4}
\end{quote}
\end{minipage} & \begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
\textbf{Conception}
\end{quote}
\end{minipage} \\
\begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
\textbf{4.4.1}
\end{quote}
\end{minipage} & \begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
\textbf{Diagrammes de s´equences}
\end{quote}
\end{minipage} \\
\multicolumn{2}{@{}>{\raggedright\arraybackslash}p{(\columnwidth - 2\tabcolsep) * \real{1.0000} + 2\tabcolsep}@{}}{%
\begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
\textbf{Diagramme de s´equence de cas d'utilisation g´erer les fichiers
d'un projet}
\end{quote}
\end{minipage}} \\
\bottomrule()
\end{longtable}

\includegraphics[width=4.29722in,height=6.91667in]{vertopal_f50a6211e6524672a7ad79ef0154e85e/media/image40.png}

\begin{longtable}[]{@{}
  >{\raggedright\arraybackslash}p{(\columnwidth - 0\tabcolsep) * \real{1.0000}}@{}}
\toprule()
\begin{minipage}[b]{\linewidth}\raggedright
Figure 4.3 \emph{-- Diagramme de cas d'utilisation raffin´e :≪G´erer les
fichiers d'un projet≫}
\end{minipage} \\
\midrule()
\endhead
\bottomrule()
\end{longtable}

58

\begin{longtable}[]{@{}
  >{\raggedright\arraybackslash}p{(\columnwidth - 0\tabcolsep) * \real{1.0000}}@{}}
\toprule()
\begin{minipage}[b]{\linewidth}\raggedright
\begin{quote}
Gestion op´erationnelle et financi` ere des projets dans Pro-Tracker
\end{quote}
\end{minipage} \\
\midrule()
\endhead
\begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
\textbf{Diagramme de s´equence pour consulter la liste des projets}
\end{quote}
\end{minipage} \\
\bottomrule()
\end{longtable}

\begin{quote}
La figure 4.4 illustre la liste des projets.
\end{quote}

\includegraphics[width=6.61111in,height=7.48472in]{vertopal_f50a6211e6524672a7ad79ef0154e85e/media/image41.png}

\begin{longtable}[]{@{}
  >{\raggedright\arraybackslash}p{(\columnwidth - 0\tabcolsep) * \real{1.0000}}@{}}
\toprule()
\begin{minipage}[b]{\linewidth}\raggedright
Figure 4.4 \emph{-- Diagramme de s´equence de cas d'utilisation
consulter la liste des projets}
\end{minipage} \\
\midrule()
\endhead
\bottomrule()
\end{longtable}

59

\begin{longtable}[]{@{}
  >{\raggedright\arraybackslash}p{(\columnwidth - 2\tabcolsep) * \real{0.5000}}
  >{\raggedright\arraybackslash}p{(\columnwidth - 2\tabcolsep) * \real{0.5000}}@{}}
\toprule()
\multicolumn{2}{@{}>{\raggedright\arraybackslash}p{(\columnwidth - 2\tabcolsep) * \real{1.0000} + 2\tabcolsep}@{}}{%
\begin{minipage}[b]{\linewidth}\raggedright
\begin{quote}
Gestion op´erationnelle et financi` ere des projets dans Pro-Tracker
\end{quote}
\end{minipage}} \\
\midrule()
\endhead
\begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
\textbf{4.4.2}
\end{quote}
\end{minipage} & \begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
\textbf{Diagramme de classes du sprint 2}
\end{quote}
\end{minipage} \\
\bottomrule()
\end{longtable}

\begin{quote}
La figure 4.5 illustre le diagramme de classes du sprint 2.
\end{quote}

\includegraphics[width=7.27222in,height=3.99722in]{vertopal_f50a6211e6524672a7ad79ef0154e85e/media/image42.png}

\begin{quote}
Figure 4.5 \emph{-- Diagramme de classes du sprint 2}

\textbf{Description du diagramme de classes}

Le diagramme de classes ci-dessous mod´elise la structure des donn´ees
de l'application. Il

comporte plusieurs entit´es principales, ainsi que leurs attributs et
relations.

\textbf{Project} : Entit´e principale repr´esentant un projet. Elle
inclut des informations sur la

priorit´e, le progr` es et le statut.etc .Un projet est g´er´e par un
profil, et peut contenir

plusieurs tˆaches, fichiers, feuilles de temps (timesheets) et
d´epenses. \textbf{Filesproject} :

Repr´esente un fichier li´e ` a un projet. Elle contient des
m´etadonn´ees telles que le nom,

le type, la taille, la date de modification et l'URL.Chaque fichier
appartient ` a un seul

projet.
\end{quote}

\begin{longtable}[]{@{}
  >{\raggedright\arraybackslash}p{(\columnwidth - 0\tabcolsep) * \real{1.0000}}@{}}
\toprule()
\begin{minipage}[b]{\linewidth}\raggedright
\begin{quote}
\textbf{Task} : D´ecrit une tˆache appartenant ` a un projet. Elle
comporte un nom, une description,

des dates de d´ebut et de fin, un type (tˆache ou bug), ainsi qu'un
statut. Chaque tˆache

est li´ee ` a un seul projet.
\end{quote}
\end{minipage} \\
\midrule()
\endhead
\bottomrule()
\end{longtable}

60

\begin{longtable}[]{@{}
  >{\raggedright\arraybackslash}p{(\columnwidth - 0\tabcolsep) * \real{1.0000}}@{}}
\toprule()
\begin{minipage}[b]{\linewidth}\raggedright
\begin{quote}
Gestion op´erationnelle et financi` ere des projets dans Pro-Tracker
\end{quote}
\end{minipage} \\
\midrule()
\endhead
\begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
\textbf{Timesheet} : Repr´esente une fiche de temps enregistrant les
heures travaill´ees sur une

op´eration ou un projet. Elle inclut la date, la description, le type,
le nombre d'heures et

si elle est facturable. Chaque timesheet est li´ee ` a un projet, une
op´eration, et un profil.

\textbf{Operation} : D´ecrit une op´eration ou activit´e. contient une
description, un nom etc.. .

Une op´eration est li´ee ` a un profil, et peut ˆetre r´ef´erenc´ee dans
plusieurs timesheets.

\textbf{Quotes} : Repr´esente un devis pr´epar´e pour un client,
incluant les articles, les montants

(sous-total, total), la devise, ainsi que l'´etat du devis.Chaque devis
est li´e ` a un seul client,

et peut donner lieu ` a une seule facture.

\textbf{Invoice} : Facture g´en´er´ee ` a partir d'un devis. Elle
contient les montants, les dates, le

statut de paiement et un lien vers les paiements associ´es. Une facture
est li´ee ` a un devis,

et peut ˆetre associ´ee ` a plusieurs paiements et d´epenses.

\textbf{Payment} : Paiement effectu´e pour une facture donn´ee. Il
contient le montant, la date

et la devise utilis´ee. Chaque paiement est li´e ` a une seule facture.
\end{quote}
\end{minipage} \\
\bottomrule()
\end{longtable}

\begin{quote}
\textbf{Customer} : Informations concernant un client, incluant le nom,
l'email, le site web,

l'adresse de facturation et les coordonn´ees. Un client peut avoir
plusieurs devis.

\textbf{Expenses} : D´epense li´ee ` a un ou plusieurs projets. Elle
inclut le nom, la description, la

date et le montant total. Chaque d´epense est li´ee ` a un projet et
peut ˆetre associ´ee ` a une

facture.
\end{quote}

\begin{longtable}[]{@{}
  >{\raggedright\arraybackslash}p{(\columnwidth - 2\tabcolsep) * \real{0.5000}}
  >{\raggedright\arraybackslash}p{(\columnwidth - 2\tabcolsep) * \real{0.5000}}@{}}
\toprule()
\begin{minipage}[b]{\linewidth}\raggedright
\begin{quote}
\textbf{4.5}
\end{quote}
\end{minipage} & \begin{minipage}[b]{\linewidth}\raggedright
\begin{quote}
\textbf{R´ealisation}
\end{quote}
\end{minipage} \\
\midrule()
\endhead
\multicolumn{2}{@{}>{\raggedright\arraybackslash}p{(\columnwidth - 2\tabcolsep) * \real{1.0000} + 2\tabcolsep}@{}}{%
\begin{minipage}[t]{\linewidth}\raggedright
Dans cette partie, nous pr´esentons les interfaces utilisateur de notre
plateforme Pro-tracker

\begin{quote}
r´ealis´ee dans la deuxi´eme sprint.

\textbf{Analyse les projets et les tˆaches}
\end{quote}

Les interfaces Projet, Tˆaches, Fichiers et ´Equipe permettent
respectivement de consulter les

d´etails du projet, de suivre les tˆaches, d'acc´eder aux documents
associ´es et de visualiser les

membres impliqu´es. L'interface Tableau de bord offre une vue
synth´etique de l'avancement

\begin{quote}
via des indicateurs cl´es comme le taux de r´ealisation des tˆaches.
\end{quote}
\end{minipage}} \\
\bottomrule()
\end{longtable}

61

\begin{longtable}[]{@{}
  >{\raggedright\arraybackslash}p{(\columnwidth - 2\tabcolsep) * \real{0.5000}}
  >{\raggedright\arraybackslash}p{(\columnwidth - 2\tabcolsep) * \real{0.5000}}@{}}
\toprule()
\multicolumn{2}{@{}>{\raggedright\arraybackslash}p{(\columnwidth - 2\tabcolsep) * \real{1.0000} + 2\tabcolsep}@{}}{%
\begin{minipage}[b]{\linewidth}\raggedright
\begin{quote}
Gestion op´erationnelle et financi` ere des projets dans Pro-Tracker
\end{quote}
\end{minipage}} \\
\midrule()
\endhead
\begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
\textbf{4.5.1}
\end{quote}
\end{minipage} & \begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
\textbf{Interface consulter projet}
\end{quote}
\end{minipage} \\
\multicolumn{2}{@{}>{\raggedright\arraybackslash}p{(\columnwidth - 2\tabcolsep) * \real{1.0000} + 2\tabcolsep}@{}}{%
Lorsque l'administrateur veut acc´eder au plateforme il peut consulter
la liste des projets. La} \\
\bottomrule()
\end{longtable}

\begin{quote}
figure 4.6 illustre l'interface de liste des projets.
\end{quote}

\includegraphics[width=5.95in,height=3.20694in]{vertopal_f50a6211e6524672a7ad79ef0154e85e/media/image43.png}

Figure 4.6 \emph{-- Interface liste des projets}

\begin{quote}
\textbf{4.5.2} \textbf{Interface tableau de board}

la figure 4.7 illustre l'interface de tableau de baord.
\end{quote}

\includegraphics[width=5.28889in,height=2.55972in]{vertopal_f50a6211e6524672a7ad79ef0154e85e/media/image44.png}

Figure 4.7 \emph{-- Interface tableau de baord}

62

\begin{longtable}[]{@{}
  >{\raggedright\arraybackslash}p{(\columnwidth - 2\tabcolsep) * \real{0.5000}}
  >{\raggedright\arraybackslash}p{(\columnwidth - 2\tabcolsep) * \real{0.5000}}@{}}
\toprule()
\multicolumn{2}{@{}>{\raggedright\arraybackslash}p{(\columnwidth - 2\tabcolsep) * \real{1.0000} + 2\tabcolsep}@{}}{%
\begin{minipage}[b]{\linewidth}\raggedright
\begin{quote}
Gestion op´erationnelle et financi` ere des projets dans Pro-Tracker
\end{quote}
\end{minipage}} \\
\midrule()
\endhead
\begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
\textbf{4.5.3}
\end{quote}
\end{minipage} & \begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
\textbf{Interface liste des tˆaches}
\end{quote}
\end{minipage} \\
\multicolumn{2}{@{}>{\raggedright\arraybackslash}p{(\columnwidth - 2\tabcolsep) * \real{1.0000} + 2\tabcolsep}@{}}{%
\begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
L'adminstrateur peut consulter la liste des tˆaches. La figure 4.8
illustre l'interface suivante.
\end{quote}
\end{minipage}} \\
\bottomrule()
\end{longtable}

\includegraphics[width=5.28889in,height=2.85in]{vertopal_f50a6211e6524672a7ad79ef0154e85e/media/image45.png}

Figure 4.8 \emph{-- Interface liste des tˆaches}

\begin{quote}
\textbf{4.5.4} \textbf{Interface liste des bugs}

L'interface liste des bugs afficher la liste des bugs, comme illustr´e `
a la figure 4.9.
\end{quote}

\includegraphics[width=5.95in,height=3.19444in]{vertopal_f50a6211e6524672a7ad79ef0154e85e/media/image46.png}

Figure 4.9 \emph{-- Interface liste des bugs}

63

\begin{longtable}[]{@{}
  >{\raggedright\arraybackslash}p{(\columnwidth - 2\tabcolsep) * \real{0.5000}}
  >{\raggedright\arraybackslash}p{(\columnwidth - 2\tabcolsep) * \real{0.5000}}@{}}
\toprule()
\multicolumn{2}{@{}>{\raggedright\arraybackslash}p{(\columnwidth - 2\tabcolsep) * \real{1.0000} + 2\tabcolsep}@{}}{%
\begin{minipage}[b]{\linewidth}\raggedright
\begin{quote}
Gestion op´erationnelle et financi` ere des projets dans Pro-Tracker
\end{quote}
\end{minipage}} \\
\midrule()
\endhead
\begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
\textbf{4.5.5}
\end{quote}
\end{minipage} & \begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
\textbf{Interface des membres du projet}
\end{quote}
\end{minipage} \\
\multicolumn{2}{@{}>{\raggedright\arraybackslash}p{(\columnwidth - 2\tabcolsep) * \real{1.0000} + 2\tabcolsep}@{}}{%
\begin{minipage}[t]{\linewidth}\raggedright
Cette interface comporte les members de l'´equipe avec le nombre des
taches r´ealiser ( nombre

\begin{quote}
de taches en attente , temin´e et en cours, comme illustr´e ` a la
figure 4.10.
\end{quote}
\end{minipage}} \\
\bottomrule()
\end{longtable}

\includegraphics[width=5.95in,height=2.85972in]{vertopal_f50a6211e6524672a7ad79ef0154e85e/media/image47.png}

Figure 4.10 \emph{-- Interface des membres du projet}

\begin{quote}
\textbf{4.5.6} \textbf{Interface des fichiers et dossiers d'un projet}
\end{quote}

L'interface des fichiers d'un projet pr´esente tous les documents
sp´ecifiques pour un projet,

\begin{quote}
comme illustr´e ` a la figure 4.11. On peur g´erer les fichiers et les
dossiers.
\end{quote}

\includegraphics[width=5.95in,height=2.58333in]{vertopal_f50a6211e6524672a7ad79ef0154e85e/media/image48.png}

Figure 4.11 \emph{-- Interface des fichiers d'un projet}

64

\begin{longtable}[]{@{}
  >{\raggedright\arraybackslash}p{(\columnwidth - 2\tabcolsep) * \real{0.5000}}
  >{\raggedright\arraybackslash}p{(\columnwidth - 2\tabcolsep) * \real{0.5000}}@{}}
\toprule()
\multicolumn{2}{@{}>{\raggedright\arraybackslash}p{(\columnwidth - 2\tabcolsep) * \real{1.0000} + 2\tabcolsep}@{}}{%
\begin{minipage}[b]{\linewidth}\raggedright
\begin{quote}
Gestion op´erationnelle et financi` ere des projets dans Pro-Tracker
\end{quote}
\end{minipage}} \\
\midrule()
\endhead
\begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
\textbf{4.6}
\end{quote}
\end{minipage} & \begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
\textbf{Test et validation}
\end{quote}
\end{minipage} \\
\begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
\textbf{4.6.1}
\end{quote}
\end{minipage} & \begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
\textbf{Test unitaire}
\end{quote}
\end{minipage} \\
\multicolumn{2}{@{}>{\raggedright\arraybackslash}p{(\columnwidth - 2\tabcolsep) * \real{1.0000} + 2\tabcolsep}@{}}{%
\begin{minipage}[t]{\linewidth}\raggedright
Un test unitaire v´erifie qu'une fonction, m´ethode ou classe fonctionne
correctement de fa¸con

\begin{quote}
isol´ee du reste de l'application.
\end{quote}
\end{minipage}} \\
\begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
\textbf{4.6.2}
\end{quote}
\end{minipage} & \begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
\textbf{Tests unitaires avec Jest}
\end{quote}
\end{minipage} \\
\multicolumn{2}{@{}>{\raggedright\arraybackslash}p{(\columnwidth - 2\tabcolsep) * \real{1.0000} + 2\tabcolsep}@{}}{%
\begin{minipage}[t]{\linewidth}\raggedright
Le framework \textbf{Jest} a ´et´e utilis´e pour r´ealiser les tests
unitaires sur les diff´erents composants de

l'application. La figure ci-dessous montre un exemple de test r´eussi
effectu´e sur le composant

\begin{quote}
ProjectsPage, qui g` ere l'affichage et le filtrage de projets.
\end{quote}
\end{minipage}} \\
\bottomrule()
\end{longtable}

\begin{longtable}[]{@{}
  >{\raggedright\arraybackslash}p{(\columnwidth - 0\tabcolsep) * \real{1.0000}}@{}}
\toprule()
\begin{minipage}[b]{\linewidth}\raggedright
\includegraphics[width=6.61111in,height=2.31667in]{vertopal_f50a6211e6524672a7ad79ef0154e85e/media/image49.png}
\end{minipage} \\
\midrule()
\endhead
\bottomrule()
\end{longtable}

\begin{quote}
Figure 4.12 \emph{-- Tests du composant ProjectsPage}

Le test valid´e dans la figure 4.12 couvre les fonctionnalit´es
suivantes du composant

ProjectsPage :

--- Le filtrage des projets selon la saisie dans la barre de recherche.

--- Le filtrage des projets par priorit´e.

--- Le filtrage des projets par statut.

Tous les cas de test ont ´et´e ex´ecut´es avec succ` es (3 sur 3), comme
l'indique le message Tests: 3

passed, 3 total, garantissant ainsi le bon fonctionnement de ces
fonctionnalit´es essentielles

pour la gestion et la visualisation des projets.
\end{quote}

65

\begin{longtable}[]{@{}
  >{\raggedright\arraybackslash}p{(\columnwidth - 2\tabcolsep) * \real{0.5000}}
  >{\raggedright\arraybackslash}p{(\columnwidth - 2\tabcolsep) * \real{0.5000}}@{}}
\toprule()
\multicolumn{2}{@{}>{\raggedright\arraybackslash}p{(\columnwidth - 2\tabcolsep) * \real{1.0000} + 2\tabcolsep}@{}}{%
\begin{minipage}[b]{\linewidth}\raggedright
\begin{quote}
Gestion op´erationnelle et financi` ere des projets dans Pro-Tracker
\end{quote}
\end{minipage}} \\
\midrule()
\endhead
\begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
\textbf{4.7}
\end{quote}
\end{minipage} & \begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
\textbf{Conclusion}
\end{quote}
\end{minipage} \\
\multicolumn{2}{@{}>{\raggedright\arraybackslash}p{(\columnwidth - 2\tabcolsep) * \real{1.0000} + 2\tabcolsep}@{}}{%
\begin{minipage}[t]{\linewidth}\raggedright
Ce chapitre a pr´esent´e les fonctionnalit´es cl´es d´evelopp´ees lors
du deuxi` eme sprint, notamment

l'authentification s´ecuris´ee des utilisateurs, la gestion des param`
etres administratifs et la ges-

tion du profil utilisateur. Ces ´el´ements assurent un contrˆole
efficace, une s´ecurit´e renforc´ee

et une personnalisation adapt´ee, posant ainsi les bases solides pour la
suite du projet. En

compl´ement, de nouvelles fonctionnalit´es ont ´et´e int´egr´ees, telles
que la gestion des finances

de la soci´et´e, permettant un suivi pr´ecis des flux financiers, ainsi
que la gestion des feuilles

de temps, offrant une meilleure visibilit´e sur les heures travaill´ees.
De plus, la gestion des

op´erations permet d'organiser les activit´es selon diff´erentes
cat´egories (r´eunions, tˆaches admi-

nistratives, etc.) et d'assigner ces op´erations aux utilisateurs,
renfor¸cant ainsi la productivit´

\begin{quote}
et la coordination des ´equipes.
\end{quote}
\end{minipage}} \\
\bottomrule()
\end{longtable}

66

\begin{longtable}[]{@{}
  >{\raggedright\arraybackslash}p{(\columnwidth - 2\tabcolsep) * \real{0.5000}}
  >{\raggedright\arraybackslash}p{(\columnwidth - 2\tabcolsep) * \real{0.5000}}@{}}
\toprule()
\begin{minipage}[b]{\linewidth}\raggedright
Chapitre\textbf{5}
\end{minipage} & \begin{minipage}[b]{\linewidth}\raggedright
\textbf{Suivi et contrˆole de l'avancement des projets}
\end{minipage} \\
\midrule()
\endhead
\bottomrule()
\end{longtable}

67

\begin{longtable}[]{@{}
  >{\raggedright\arraybackslash}p{(\columnwidth - 2\tabcolsep) * \real{0.5000}}
  >{\raggedright\arraybackslash}p{(\columnwidth - 2\tabcolsep) * \real{0.5000}}@{}}
\toprule()
\multicolumn{2}{@{}>{\raggedright\arraybackslash}p{(\columnwidth - 2\tabcolsep) * \real{1.0000} + 2\tabcolsep}@{}}{%
\begin{minipage}[b]{\linewidth}\raggedright
\begin{quote}
Suivi et contrˆole de l'avancement des projets
\end{quote}
\end{minipage}} \\
\midrule()
\endhead
\begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
\textbf{5.1}
\end{quote}
\end{minipage} & \begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
\textbf{Introduction}
\end{quote}
\end{minipage} \\
\multicolumn{2}{@{}>{\raggedright\arraybackslash}p{(\columnwidth - 2\tabcolsep) * \real{1.0000} + 2\tabcolsep}@{}}{%
Le troisi` eme sprint de d´eveloppement de la plateforme Pro-Tracker,
d'une dur´ee de trois

semaines, a port´e sur l'int´egration de modules de visualisation
avanc´ee pour les projets et

les membres. Ce sprint a permis d'impl´ementer des fonctionnalit´es
cl´es telles que : le tableau} \\
\bottomrule()
\end{longtable}

\begin{quote}
de bord KPIs, la gestion des projets financiers, la gestion des jalons,
les notifications.
\end{quote}

Ces ajouts visent ` a am´eliorer le pilotage, la performance et la
r´eactivit´e des utilisateurs au

\begin{quote}
sein de la plateforme.
\end{quote}

\begin{longtable}[]{@{}
  >{\raggedright\arraybackslash}p{(\columnwidth - 2\tabcolsep) * \real{0.5000}}
  >{\raggedright\arraybackslash}p{(\columnwidth - 2\tabcolsep) * \real{0.5000}}@{}}
\toprule()
\begin{minipage}[b]{\linewidth}\raggedright
\begin{quote}
\textbf{5.2}
\end{quote}
\end{minipage} & \begin{minipage}[b]{\linewidth}\raggedright
\begin{quote}
\textbf{Backlog du sprint 3}
\end{quote}
\end{minipage} \\
\midrule()
\endhead
\multicolumn{2}{@{}>{\raggedright\arraybackslash}p{(\columnwidth - 2\tabcolsep) * \real{1.0000} + 2\tabcolsep}@{}}{%
\begin{minipage}[t]{\linewidth}\raggedright
Le backlog du Sprint 3 de la plateforme Pro-Tracker comprend
l'int´egration de plusieurs

modules destin´es ` a renforcer la visualisation et le suivi des
donn´ees li´ees aux projets et aux

membres. Ce sprint pr´evoit la mise en place d'un tableau de bord
regroupant des indicateurs

cl´es de performance (KPIs), la gestion des projets financiers, ainsi
que le suivi des jalons

(milestones) pour chaque projet. Il inclut ´egalement un syst` eme de
suivi de pr´esence, avec

la possibilit´e de justifier les absences, ainsi qu'un syst` eme de
notifications pour informer les

utilisateurs des ´ev´enements importants. Un chatbot est ´egalement
int´egr´e pour assister les

utilisateurs dans la navigation et l'acc` es aux informations. Ce sprint
vise ` a am´eliorer la lisibilit´

\begin{quote}
des donn´ees, la coordination des ´equipes et l'efficacit´e du suivi
op´erationnel.
\end{quote}

Table 5.1 \emph{-- Backlog de sprint3- Syst` eme de visualisation des
donn´ees pour membres et projets}

\begin{longtable}[]{@{}
  >{\raggedright\arraybackslash}p{(\columnwidth - 4\tabcolsep) * \real{0.3333}}
  >{\raggedright\arraybackslash}p{(\columnwidth - 4\tabcolsep) * \real{0.3333}}
  >{\raggedright\arraybackslash}p{(\columnwidth - 4\tabcolsep) * \real{0.3333}}@{}}
\toprule()
\begin{minipage}[b]{\linewidth}\raggedright
\begin{quote}
\textbf{Feature}
\end{quote}
\end{minipage} & \begin{minipage}[b]{\linewidth}\raggedright
\begin{quote}
\textbf{User Story}
\end{quote}
\end{minipage} & \begin{minipage}[b]{\linewidth}\raggedright
\begin{quote}
\textbf{Estimation (jours)}
\end{quote}
\end{minipage} \\
\midrule()
\endhead
\begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
\textbf{Projet :Jalons (Milestones)}
\end{quote}
\end{minipage} & \begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
- En tant qu'administrateur, je veux d´efinir les jalons cl´es pour
chaque projet afin d'en assurer le suivi structur´e.
\end{quote}
\end{minipage} & 2 \\
\begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
\textbf{Projet : Dashboard KPIs}
\end{quote}
\end{minipage} & \begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
- En tant qu'administrateur, je veux acc´eder ` a un

tableau de bord regroupant les indicateurs cl´es de

performance (KPIs) .
\end{quote}
\end{minipage} & 3 \\
\begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
\textbf{Dashboard Pro-Tracker}
\end{quote}
\end{minipage} & \begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
- En tant qu'administrateur, je veux acc´eder ` a un
\end{quote}

tableau de bord regroupant les projets et les finance

\begin{quote}
de la soci´et´e.
\end{quote}
\end{minipage} & 2 \\
\bottomrule()
\end{longtable}
\end{minipage}} \\
\bottomrule()
\end{longtable}

68

\begin{longtable}[]{@{}
  >{\raggedright\arraybackslash}p{(\columnwidth - 0\tabcolsep) * \real{1.0000}}@{}}
\toprule()
\begin{minipage}[b]{\linewidth}\raggedright
\begin{quote}
Suivi et contrˆole de l'avancement des projets
\end{quote}
\end{minipage} \\
\midrule()
\endhead
\bottomrule()
\end{longtable}

\begin{longtable}[]{@{}
  >{\raggedright\arraybackslash}p{(\columnwidth - 4\tabcolsep) * \real{0.3333}}
  >{\raggedright\arraybackslash}p{(\columnwidth - 4\tabcolsep) * \real{0.3333}}
  >{\raggedright\arraybackslash}p{(\columnwidth - 4\tabcolsep) * \real{0.3333}}@{}}
\toprule()
\begin{minipage}[b]{\linewidth}\raggedright
\textbf{Projets :Financiers}
\end{minipage} & \begin{minipage}[b]{\linewidth}\raggedright
\begin{quote}
- En tant qu'administrateur, je veux consulter le

budget initial, les d´epenses engag´ees et le solde
\end{quote}

restant pour chaque projet afin de surveiller la sant´

\begin{quote}
financi` ere.

- En tant qu'administrateur, je veux g´erer les

d´epenses .
\end{quote}
\end{minipage} & \begin{minipage}[b]{\linewidth}\raggedright
2.5

1
\end{minipage} \\
\midrule()
\endhead
\begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
\textbf{Equipe´}
\end{quote}
\end{minipage} & \begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
- En tant qu'administrateur, je veux consulter la liste des members .

- En tant qu'administrateur, je veux consulter les informations
sp´ecifiques d'un membre.

- En tant qu'administrateur, je veux consulter les projets actif d'un
membre .

- En tant qu'administrateur, je veux consulter les qualit´e de travail
d'un membre .

- En tant qu'administrateur, je veux consulter les acitivt´es r´ecente
d'un membre.

- En tant qu'administrateur, je souhaite g´erer les fichiers et les
dossiers d'un membre sp´ecifique afin d'organiser et de maintenir ses
ressources.
\end{quote}
\end{minipage} & 1

0.5

0.5

2

1

2 \\
\begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
\textbf{Equipe : Gestion´}

\textbf{des blˆames ou}

\textbf{r´ealisations}
\end{quote}
\end{minipage} & \begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
- En tant qu'administrateur, je veux ajouter blˆame

ou r´ealisation (achievement) pour un membre.
\end{quote}

- En tant qu'administrateur, je veux modifier blˆame

\begin{quote}
ou r´ealisation (achievement) pour un membre .

- En tant qu'administrateur, je veux supprimer

blˆame ou r´ealisation pour un membre .
\end{quote}

- En tant qu'administrateur, je veux consulter blˆame

\begin{quote}
ou r´ealisation (achievement) pour un membre .

- En tant qu'administrateur, je veux ajouter score

pour blˆame ou r´ealisations (achievement) pour un

membre .
\end{quote}
\end{minipage} & 1

1

1

1

1 \\
\bottomrule()
\end{longtable}

69

\begin{longtable}[]{@{}
  >{\raggedright\arraybackslash}p{(\columnwidth - 0\tabcolsep) * \real{1.0000}}@{}}
\toprule()
\begin{minipage}[b]{\linewidth}\raggedright
\begin{quote}
Suivi et contrˆole de l'avancement des projets
\end{quote}
\end{minipage} \\
\midrule()
\endhead
\bottomrule()
\end{longtable}

\begin{longtable}[]{@{}
  >{\raggedright\arraybackslash}p{(\columnwidth - 4\tabcolsep) * \real{0.3333}}
  >{\raggedright\arraybackslash}p{(\columnwidth - 4\tabcolsep) * \real{0.3333}}
  >{\raggedright\arraybackslash}p{(\columnwidth - 4\tabcolsep) * \real{0.3333}}@{}}
\toprule()
\begin{minipage}[b]{\linewidth}\raggedright
\begin{quote}
\textbf{Equipe : Suivi de´}

\textbf{pr´esence}

\textbf{(Attendance)}
\end{quote}
\end{minipage} & \begin{minipage}[b]{\linewidth}\raggedright
\begin{quote}
- En tant qu'administrateur, je veux que le syst` eme

compare automatiquement les heures de \emph{check-in} et
\end{quote}

\emph{check-out} de chaque membre aux horaires de travail

\begin{quote}
standards d´efinis au Sprint 1, afin de g´en´erer un

statut journalier tel que \emph{``Arriv´ee en retard''},

\emph{``Heures suffisantes''} ou \emph{``Absent''}.

- En tant qu'administrateur, je veux que le syst` eme

calcule automatiquement un score de pr´esence

journalier en fonction du statut g´en´er´e, afin

d'´evaluer la ponctualit´e et l'assiduit´e des membres

de mani` ere quantitative.
\end{quote}
\end{minipage} & \begin{minipage}[b]{\linewidth}\raggedright
1.5

2
\end{minipage} \\
\midrule()
\endhead
\begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
\textbf{Equipe :´}

\textbf{Justifications}

\textbf{d'absence}
\end{quote}
\end{minipage} & \begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
- En tant qu'administrateur, je veux ajouter une justification
d'absence.

- En tant qu'administrateur, je veux consulter une justification
d'absence.

- En tant qu'administrateur, je veux modifier une justification
d'absence.

- En tant qu'administrateur, je veux supprimer une justification
d'absence.
\end{quote}
\end{minipage} & 1

1

1

1 \\
\begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
\textbf{Equipe : finance´}
\end{quote}
\end{minipage} & \begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
- En tant qu'administrateur, je veux consulter la performance d'un
membre .

- En tant qu'administrateur, je veux modifier le cout d'un membre.
\end{quote}
\end{minipage} & 2

1 \\
\begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
\textbf{Notifications}
\end{quote}
\end{minipage} & \begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
- En tant qu'administrateur, je veux consulter les

notifications via le syst` eme de notification lors des

op´erations r´ealiser .
\end{quote}
\end{minipage} & 4 \\
\begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
\textbf{Chatbot}
\end{quote}
\end{minipage} & \begin{minipage}[t]{\linewidth}\raggedright
- En tant qu'administrateur, je veux g´erer la base de

\begin{quote}
connaissances utilis´ee par le chatbot

(questions/r´eponses).
\end{quote}
\end{minipage} & 5 \\
\bottomrule()
\end{longtable}

70

\begin{longtable}[]{@{}
  >{\raggedright\arraybackslash}p{(\columnwidth - 2\tabcolsep) * \real{0.5000}}
  >{\raggedright\arraybackslash}p{(\columnwidth - 2\tabcolsep) * \real{0.5000}}@{}}
\toprule()
\multicolumn{2}{@{}>{\raggedright\arraybackslash}p{(\columnwidth - 2\tabcolsep) * \real{1.0000} + 2\tabcolsep}@{}}{%
\begin{minipage}[b]{\linewidth}\raggedright
\begin{quote}
Suivi et contrˆole de l'avancement des projets
\end{quote}
\end{minipage}} \\
\midrule()
\endhead
\begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
\textbf{5.3}
\end{quote}
\end{minipage} & \begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
\textbf{Sp´ecification fonctionnelle}
\end{quote}
\end{minipage} \\
\multicolumn{2}{@{}>{\raggedright\arraybackslash}p{(\columnwidth - 2\tabcolsep) * \real{1.0000} + 2\tabcolsep}@{}}{%
\begin{minipage}[t]{\linewidth}\raggedright
Dans cette section, nous abordons les fonctionnalit´es du troixi` eme
sprint. Nous commen¸cons

\begin{quote}
par pr´esenter le diagramme de cas d'utilisation de ce sprint.
\end{quote}
\end{minipage}} \\
\begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
\textbf{5.3.1}
\end{quote}
\end{minipage} & \begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
\textbf{Diagramme de cas d'utilisation du troixi` eme sprint}
\end{quote}
\end{minipage} \\
\multicolumn{2}{@{}>{\raggedright\arraybackslash}p{(\columnwidth - 2\tabcolsep) * \real{1.0000} + 2\tabcolsep}@{}}{%
\begin{minipage}[t]{\linewidth}\raggedright
Le diagramme de cas d'utilisation illustre les actions possibles des
utilisateurs pour le troixi` eme

sprint, regroupant toutes les fonctionnalit´es pr´evues. Les cas
d'utilisation qui me sont attribu´es,

\begin{quote}
selon la r´epartition entre d´eveloppeurs, sont en blanc.
\end{quote}
\end{minipage}} \\
\bottomrule()
\end{longtable}

\includegraphics[width=6.61111in,height=4.625in]{vertopal_f50a6211e6524672a7ad79ef0154e85e/media/image50.png}

\begin{longtable}[]{@{}
  >{\raggedright\arraybackslash}p{(\columnwidth - 0\tabcolsep) * \real{1.0000}}@{}}
\toprule()
\begin{minipage}[b]{\linewidth}\raggedright
Figure 5.1 \emph{-- diagramme de cas d'utilisation du troixi` eme
sprint}

Ce diagramme pr´esente les cas d'utilisation accessibles ` a
l'administrateur durant le sprint 3,

centr´es sur la gestion des membres (pr´esence, qualit´e de travail,
projets actifs), la gestion des

notifications, ainsi que le suivi des finances du projet et des membres.
Toutes les fonctionnalit´es

\begin{quote}
n´ecessitent une authentification pr´ealable.
\end{quote}
\end{minipage} \\
\midrule()
\endhead
\bottomrule()
\end{longtable}

71

\begin{longtable}[]{@{}
  >{\raggedright\arraybackslash}p{(\columnwidth - 4\tabcolsep) * \real{0.3333}}
  >{\raggedright\arraybackslash}p{(\columnwidth - 4\tabcolsep) * \real{0.3333}}
  >{\raggedright\arraybackslash}p{(\columnwidth - 4\tabcolsep) * \real{0.3333}}@{}}
\toprule()
\multicolumn{3}{@{}>{\raggedright\arraybackslash}p{(\columnwidth - 4\tabcolsep) * \real{1.0000} + 4\tabcolsep}@{}}{%
\begin{minipage}[b]{\linewidth}\raggedright
\begin{quote}
Suivi et contrˆole de l'avancement des projets
\end{quote}
\end{minipage}} \\
\midrule()
\endhead
\multicolumn{3}{@{}>{\raggedright\arraybackslash}p{(\columnwidth - 4\tabcolsep) * \real{1.0000} + 4\tabcolsep}@{}}{%
\begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
\textbf{Raffinement de cas d'utilisation}\emph{≪}\textbf{Gestion de
justification de pr´esence}\emph{≫}
\end{quote}
\end{minipage}} \\
\begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
La figure
\end{quote}
\end{minipage} & \begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
5.2 illustre le raffinement du cas d'utilisation
\end{quote}
\end{minipage} & \begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
\emph{≪}Gestion de justification de
\end{quote}
\end{minipage} \\
\multicolumn{3}{@{}>{\raggedright\arraybackslash}p{(\columnwidth - 4\tabcolsep) * \real{1.0000} + 4\tabcolsep}@{}}{%
\begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
pr´esence\emph{≫}afin d'expliquer son aspect fonctionnel.
\end{quote}
\end{minipage}} \\
\bottomrule()
\end{longtable}

\includegraphics[width=5.28889in,height=4.38055in]{vertopal_f50a6211e6524672a7ad79ef0154e85e/media/image51.png}

Figure 5.2 \emph{-- Diagramme de cas d'utilisation raffin´e :≪Gestion de
justification de pr´esence≫}

\begin{quote}
\textbf{Raffinement de cas d'utilisation}\emph{≪}\textbf{Gestion des
finances du projet}\emph{≫}

La figure 5.3 illustre le raffinement du cas
d'utilisation\emph{≪≪}Gestion des finances du projet\emph{≫}afin
d'expliquer son aspect fonctionnel.
\end{quote}

72

\begin{longtable}[]{@{}
  >{\raggedright\arraybackslash}p{(\columnwidth - 0\tabcolsep) * \real{1.0000}}@{}}
\toprule()
\begin{minipage}[b]{\linewidth}\raggedright
\begin{quote}
Suivi et contrˆole de l'avancement des projets
\end{quote}
\end{minipage} \\
\midrule()
\endhead
\bottomrule()
\end{longtable}

\includegraphics[width=6.61111in,height=6.09167in]{vertopal_f50a6211e6524672a7ad79ef0154e85e/media/image52.png}

\begin{longtable}[]{@{}
  >{\raggedright\arraybackslash}p{(\columnwidth - 2\tabcolsep) * \real{0.5000}}
  >{\raggedright\arraybackslash}p{(\columnwidth - 2\tabcolsep) * \real{0.5000}}@{}}
\toprule()
\multicolumn{2}{@{}>{\raggedright\arraybackslash}p{(\columnwidth - 2\tabcolsep) * \real{1.0000} + 2\tabcolsep}@{}}{%
\begin{minipage}[b]{\linewidth}\raggedright
Figure 5.3 \emph{-- Diagramme de cas d'utilisation raffin´e :≪Gestion
des finances du projet≫}
\end{minipage}} \\
\midrule()
\endhead
\begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
\textbf{5.3.2}
\end{quote}
\end{minipage} & \begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
\textbf{Desciption textuelle}
\end{quote}
\end{minipage} \\
\multicolumn{2}{@{}>{\raggedright\arraybackslash}p{(\columnwidth - 2\tabcolsep) * \real{1.0000} + 2\tabcolsep}@{}}{%
\begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
\textbf{Description textuelle de cas d'utilisation -- Suivi de pr´esence
des membres}
\end{quote}

Table 5.2 \emph{-- Description textuelle --Suivi de pr´esence des
membres}

\begin{longtable}[]{@{}
  >{\raggedright\arraybackslash}p{(\columnwidth - 2\tabcolsep) * \real{0.5000}}
  >{\raggedright\arraybackslash}p{(\columnwidth - 2\tabcolsep) * \real{0.5000}}@{}}
\toprule()
\begin{minipage}[b]{\linewidth}\raggedright
\begin{quote}
\textbf{El´´ement}
\end{quote}
\end{minipage} & \begin{minipage}[b]{\linewidth}\raggedright
\begin{quote}
\textbf{Description}
\end{quote}
\end{minipage} \\
\midrule()
\endhead
\begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
Titre
\end{quote}
\end{minipage} & \begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
Suivi de pr´esence pour chaque membre
\end{quote}
\end{minipage} \\
\bottomrule()
\end{longtable}
\end{minipage}} \\
\bottomrule()
\end{longtable}

73

\begin{longtable}[]{@{}
  >{\raggedright\arraybackslash}p{(\columnwidth - 0\tabcolsep) * \real{1.0000}}@{}}
\toprule()
\begin{minipage}[b]{\linewidth}\raggedright
\begin{quote}
Suivi et contrˆole de l'avancement des projets
\end{quote}
\end{minipage} \\
\midrule()
\endhead
\bottomrule()
\end{longtable}

\begin{longtable}[]{@{}
  >{\raggedright\arraybackslash}p{(\columnwidth - 2\tabcolsep) * \real{0.5000}}
  >{\raggedright\arraybackslash}p{(\columnwidth - 2\tabcolsep) * \real{0.5000}}@{}}
\toprule()
\begin{minipage}[b]{\linewidth}\raggedright
\begin{quote}
Acteur
\end{quote}
\end{minipage} & \begin{minipage}[b]{\linewidth}\raggedright
\begin{quote}
Administrateur
\end{quote}
\end{minipage} \\
\midrule()
\endhead
\begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
Pr´e conditions
\end{quote}
\end{minipage} & \begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
--- L'administrateur est authentifi´e dans le syst` eme.
\end{quote}

--- Le syst` eme poss` ede des donn´ees de pointage (heure

\begin{quote}
d'arriv´ee et de d´epart).
\end{quote}
\end{minipage} \\
\begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
Sc´enario nominal
\end{quote}
\end{minipage} & \begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
--- L'administrateur acc` ede ` a la section\emph{≪}pr´esence\emph{≫}.

--- Le syst` eme affiche un tableau contenant :

--- La date de chaque jour de travail.

--- L'heure d'arriv´ee et de d´epart.

--- Le total des heures travaill´ees.

--- L'´etat (conforme, retard, d´epart anticip´e,

heures insuffisantes).

--- Le score (par exemple : 2/3).

--- Les actions disponibles : Justifier, Voir les jus-

tifications.
\end{quote}

--- L'administrateur peut cliquer sur\emph{≪}Justifier\emph{≫}pour

\begin{quote}
soumettre une justification.
\end{quote}

--- L'administrateur peut consulter les justifications

\begin{quote}
d´ej` a fournies.
\end{quote}
\end{minipage} \\
\begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
Post conditions
\end{quote}
\end{minipage} & \begin{minipage}[t]{\linewidth}\raggedright
--- Les justifications sont enregistr´ees et associ´ees aux

\begin{quote}
lignes concern´ees.
\end{quote}

--- Le statut et le score de pr´esence sons ajouter et calcu-

\begin{quote}
ler a partir de heure d'arriv´ee , heure de d´epart , date

de pointage et comparer avec horaire de travailet ou

peut ˆetre mis ` a jour en fonction des justifications

accept´ees.
\end{quote}
\end{minipage} \\
\bottomrule()
\end{longtable}

74

\begin{longtable}[]{@{}
  >{\raggedright\arraybackslash}p{(\columnwidth - 2\tabcolsep) * \real{0.5000}}
  >{\raggedright\arraybackslash}p{(\columnwidth - 2\tabcolsep) * \real{0.5000}}@{}}
\toprule()
\multicolumn{2}{@{}>{\raggedright\arraybackslash}p{(\columnwidth - 2\tabcolsep) * \real{1.0000} + 2\tabcolsep}@{}}{%
\begin{minipage}[b]{\linewidth}\raggedright
\begin{quote}
Suivi et contrˆole de l'avancement des projets
\end{quote}
\end{minipage}} \\
\midrule()
\endhead
\begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
\textbf{5.4}
\end{quote}
\end{minipage} & \begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
\textbf{Conception}
\end{quote}
\end{minipage} \\
\begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
\textbf{5.4.1}
\end{quote}
\end{minipage} & \begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
\textbf{Diagramme de s´equence}
\end{quote}
\end{minipage} \\
\multicolumn{2}{@{}>{\raggedright\arraybackslash}p{(\columnwidth - 2\tabcolsep) * \real{1.0000} + 2\tabcolsep}@{}}{%
\begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
\textbf{Diagramme de s´equence de cas d'utilisation ajouter une
d´epense}
\end{quote}
\end{minipage}} \\
\bottomrule()
\end{longtable}

\includegraphics[width=6.61111in,height=6.74028in]{vertopal_f50a6211e6524672a7ad79ef0154e85e/media/image53.png}

\begin{longtable}[]{@{}
  >{\raggedright\arraybackslash}p{(\columnwidth - 0\tabcolsep) * \real{1.0000}}@{}}
\toprule()
\begin{minipage}[b]{\linewidth}\raggedright
Figure 5.4 \emph{-- Diagramme de s´equence de cas d'utilisation ajouter
une d´epense}
\end{minipage} \\
\midrule()
\endhead
\bottomrule()
\end{longtable}

75

\begin{longtable}[]{@{}
  >{\raggedright\arraybackslash}p{(\columnwidth - 2\tabcolsep) * \real{0.5000}}
  >{\raggedright\arraybackslash}p{(\columnwidth - 2\tabcolsep) * \real{0.5000}}@{}}
\toprule()
\multicolumn{2}{@{}>{\raggedright\arraybackslash}p{(\columnwidth - 2\tabcolsep) * \real{1.0000} + 2\tabcolsep}@{}}{%
\begin{minipage}[b]{\linewidth}\raggedright
\begin{quote}
Suivi et contrˆole de l'avancement des projets
\end{quote}
\end{minipage}} \\
\midrule()
\endhead
\begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
\textbf{5.4.2}
\end{quote}
\end{minipage} & \begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
\textbf{Diagramme de classes du sprint 3}
\end{quote}
\end{minipage} \\
\bottomrule()
\end{longtable}

\begin{quote}
La figure 5.5 illustre le digramme de classes de sprint 3.
\end{quote}

\includegraphics[width=7.27083in,height=4.46389in]{vertopal_f50a6211e6524672a7ad79ef0154e85e/media/image54.png}

\begin{quote}
Figure 5.5 \emph{-- Diagramme de classes du sprint 3}

\textbf{Description du diagramme de classes}

Le diagramme de classes suivant mod´elise un syst` eme de gestion RH
avec les entit´es principales

d´ecrites ci-dessous :

--- \textbf{Profile} : Repr´esente un utilisateur avec les attributs
\emph{id, FirstName, LastName, Avatar,}

\emph{cost, Departement, Joindate, Phone, skills, status}.

--- Associations : poss` ede plusieurs \textbf{Achievements},
\textbf{Blames}, \textbf{Notifications}, \textbf{Atten-}

\textbf{dances}, et \textbf{Membercosts}.
\end{quote}

\begin{longtable}[]{@{}
  >{\raggedright\arraybackslash}p{(\columnwidth - 0\tabcolsep) * \real{1.0000}}@{}}
\toprule()
\begin{minipage}[b]{\linewidth}\raggedright
\begin{quote}
--- \textbf{Milestone} : Contient \emph{id, name, startdate, enddate},
li´e ` a un \textbf{Project}.

--- \textbf{Achievement} : Repr´esente une r´ealisation avec \emph{id,
title, description, date, Impactlevel},

associ´ee ` a un \textbf{Profile}.
\end{quote}
\end{minipage} \\
\midrule()
\endhead
\bottomrule()
\end{longtable}

76

\begin{longtable}[]{@{}
  >{\raggedright\arraybackslash}p{(\columnwidth - 0\tabcolsep) * \real{1.0000}}@{}}
\toprule()
\begin{minipage}[b]{\linewidth}\raggedright
\begin{quote}
Suivi et contrˆole de l'avancement des projets
\end{quote}
\end{minipage} \\
\midrule()
\endhead
\begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
--- \textbf{Blame} : Repr´esente un avertissement avec \emph{id, title,
description, date, Severitylevel}, li´
\end{quote}
\end{minipage} \\
\bottomrule()
\end{longtable}

\begin{quote}
a un \textbf{Profile}.

--- \textbf{Notification} : Contient \emph{id, name, description, date},
adress´ee ` a un \textbf{Profile}.

--- \textbf{Attendance} : G` ere la pr´esence via \emph{id, Checkin,
Checkout, Dateattendance,}

\emph{Hoursworked, score, status}.

--- Li´ee ` a un \textbf{Profile} et peut inclure des
\textbf{Justifications}.

--- \textbf{Justification} : Documente une retard avec \emph{id, title,
file, heuredebut, heurefin}, li´ee `

une \textbf{Attendance}.

--- \textbf{Membercost} : Contient les coˆuts individuels avec \emph{id,
Begintime, Endtime, Cost}, li´

a un \textbf{Profile}.

\textbf{5.5} \textbf{R´ealisation}
\end{quote}

Dans cette partie, nous pr´esentons les interfaces utilisateur de notre
plateforme Pro-tracker

r´ealis´ee dans la troisi` eme sprint. Ces captures permettent une
visualisation directe des

\begin{quote}
diff´erentes fonctionnalit´es et des interactions possibles avec
l'application.

\textbf{5.5.1} \textbf{Analyse des Milestones et KPIs et finance des
Projets}
\end{quote}

Pour chaque projet, des \textbf{milestones} ont ´et´e d´efinies afin de
suivre les ´etapes cl´es et d'assurer

un respect des d´elais. Des \textbf{KPIs} sp´ecifiques ont ´egalement
´et´e mis en place pour ´evaluer la

\begin{quote}
progression, la qualit´e et l'efficacit´e de la gestion de projet.

\textbf{Interface consulter milestones}
\end{quote}

\begin{longtable}[]{@{}
  >{\raggedright\arraybackslash}p{(\columnwidth - 0\tabcolsep) * \real{1.0000}}@{}}
\toprule()
\begin{minipage}[b]{\linewidth}\raggedright
Lorsque l'administrateur veut acc´eder ` a la plateforme, il peut
consulter la liste jetons et voir

\begin{quote}
si ces jetons seront livr´es ` a temps ou non. La figure 5.6 illustre
l'interface des Jalons.
\end{quote}
\end{minipage} \\
\midrule()
\endhead
\bottomrule()
\end{longtable}

77

\begin{longtable}[]{@{}
  >{\raggedright\arraybackslash}p{(\columnwidth - 0\tabcolsep) * \real{1.0000}}@{}}
\toprule()
\begin{minipage}[b]{\linewidth}\raggedright
\begin{quote}
Suivi et contrˆole de l'avancement des projets
\end{quote}
\end{minipage} \\
\midrule()
\endhead
\bottomrule()
\end{longtable}

\includegraphics[width=5.95in,height=2.84722in]{vertopal_f50a6211e6524672a7ad79ef0154e85e/media/image55.png}

Figure 5.6 \emph{-- Interface de liste des Jalons}

\begin{quote}
\textbf{Interface des indicateurs de performance}
\end{quote}

L'interface des indicateurs de performance pr´esente de mani` ere claire
et synth´etique les indi-

cateurs cl´es de performance du projet. Elle permet ` a l'administrateur
de suivre en temps r´eel

l'´evolution des activit´es et d'identifier rapidement les ´ecarts ou
retards ´eventuels.La figure 5.7

illustre l'interface d´estimation de pr´ecision et deja pr´esenter les
autres indicateurs au dessous

\begin{quote}
de cette kpi.
\end{quote}

\includegraphics[width=5.95in,height=2.90972in]{vertopal_f50a6211e6524672a7ad79ef0154e85e/media/image56.png}

Figure 5.7 \emph{-- Interface des indicateurs de performance}

78

\begin{longtable}[]{@{}
  >{\raggedright\arraybackslash}p{(\columnwidth - 0\tabcolsep) * \real{1.0000}}@{}}
\toprule()
\begin{minipage}[b]{\linewidth}\raggedright
\begin{quote}
Suivi et contrˆole de l'avancement des projets
\end{quote}
\end{minipage} \\
\midrule()
\endhead
\bottomrule()
\end{longtable}

\begin{quote}
\textbf{Interface de finance des projets}
\end{quote}

L'interface de gestion financi` ere du projet illustre les donn´ees
financi` eres sp´ecifiques li´ees ` a un

\begin{quote}
projet donn´e, comme illustre la figure suivante 5.8.
\end{quote}

\includegraphics[width=5.95in,height=2.84583in]{vertopal_f50a6211e6524672a7ad79ef0154e85e/media/image57.png}

Figure 5.8 \emph{-- Interface de finance des projets}

\begin{quote}
\textbf{5.5.2} \textbf{Les membres et leurs d´etails}

\textbf{Interface liste des membres}
\end{quote}

L'interface liste des membres permet au administrateurs de chercher ou
consulter la liste des

\begin{quote}
membres, comme illustre la figure suivante 5.9.
\end{quote}

\includegraphics[width=5.95in,height=2.50278in]{vertopal_f50a6211e6524672a7ad79ef0154e85e/media/image58.png}

Figure 5.9 \emph{-- Interface liste des membres}

79

\begin{longtable}[]{@{}
  >{\raggedright\arraybackslash}p{(\columnwidth - 0\tabcolsep) * \real{1.0000}}@{}}
\toprule()
\begin{minipage}[b]{\linewidth}\raggedright
\begin{quote}
Suivi et contrˆole de l'avancement des projets
\end{quote}
\end{minipage} \\
\midrule()
\endhead
\begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
\textbf{Interface Suivi de pr´esence des membres}
\end{quote}

Cette interface affiche l'historique de pr´esence d'un membre, avec des
colonnes pr´ecisant la

date, l'heure d'arriv´ee, l'heure de d´epart, les heures travaill´ees,
le statut de conformit´e, un

\begin{quote}
score de pr´esence sur 3. La figure 5.10 illustre l'interface suivante.
\end{quote}
\end{minipage} \\
\bottomrule()
\end{longtable}

\includegraphics[width=6.61111in,height=2.42639in]{vertopal_f50a6211e6524672a7ad79ef0154e85e/media/image59.png}

Figure 5.10 \emph{-- Interface Suivi de pr´esence des membre}

\begin{quote}
\textbf{Interfaces pour ajouter ou modifier une justification}
\end{quote}

Ces interfaces permettent ` a l'utilisateur de saisir, ´editer ou
valider les motifs justifiant une

\begin{quote}
absence ou une action particuli` ere, comme illustr´e aux figures
suivantes 5.11 et 5.12.

\includegraphics[width=3.17361in,height=2.37222in]{vertopal_f50a6211e6524672a7ad79ef0154e85e/media/image60.png}

Figure 5.11 \emph{-- Interface ajouter justification}

\includegraphics[width=3.17361in,height=2.49583in]{vertopal_f50a6211e6524672a7ad79ef0154e85e/media/image61.png}

Figure 5.12 \emph{-- Interface modifier justification}
\end{quote}

80

\begin{longtable}[]{@{}
  >{\raggedright\arraybackslash}p{(\columnwidth - 2\tabcolsep) * \real{0.5000}}
  >{\raggedright\arraybackslash}p{(\columnwidth - 2\tabcolsep) * \real{0.5000}}@{}}
\toprule()
\multicolumn{2}{@{}>{\raggedright\arraybackslash}p{(\columnwidth - 2\tabcolsep) * \real{1.0000} + 2\tabcolsep}@{}}{%
\begin{minipage}[b]{\linewidth}\raggedright
\begin{quote}
Suivi et contrˆole de l'avancement des projets
\end{quote}
\end{minipage}} \\
\midrule()
\endhead
\begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
\textbf{5.5.3}
\end{quote}
\end{minipage} & \begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
\textbf{Chatbot intelligent}
\end{quote}
\end{minipage} \\
\multicolumn{2}{@{}>{\raggedright\arraybackslash}p{(\columnwidth - 2\tabcolsep) * \real{1.0000} + 2\tabcolsep}@{}}{%
\begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
\textbf{Pr´esentation des Mod` eles}
\end{quote}

Notre syst` eme repose sur une architecture hybride combinant deux mod`
eles compl´ementaires :

\begin{quote}
--- \textbf{Llama3-70b} : un mod` ele de langage large (LLM) de derni`
ere g´en´eration d´evelopp´e par

Meta, utilis´e ici pour la g´en´eration de r´eponses naturelles,
coh´erentes et contextuelles.

Ce mod` ele poss` ede 70 milliards de param` etres, offrant une
compr´ehension approfondie

du langage et une capacit´e de g´en´eration de texte avanc´ee.

--- \textbf{all-mpnet-base-v2} : un mod` ele d'embedding issu de la
famille Sentence Transformers,

optimis´e pour la recherche s´emantique. Il permet de transformer les
questions et docu-

ments en vecteurs num´eriques afin de retrouver efficacement les
informations les plus
\end{quote}
\end{minipage}} \\
\bottomrule()
\end{longtable}

\begin{quote}
pertinentes dans la base de connaissances.

\textbf{Diagramme d'Architecture}
\end{quote}

\includegraphics[width=6.61111in,height=3.175in]{vertopal_f50a6211e6524672a7ad79ef0154e85e/media/image62.png}

\begin{longtable}[]{@{}
  >{\raggedright\arraybackslash}p{(\columnwidth - 0\tabcolsep) * \real{1.0000}}@{}}
\toprule()
\begin{minipage}[b]{\linewidth}\raggedright
Figure 5.13 \emph{-- Diagramme d'Architecture du syst` eme RAG
(Retrieval-Augmented Generation)}
\end{minipage} \\
\midrule()
\endhead
\bottomrule()
\end{longtable}

81

\begin{longtable}[]{@{}
  >{\raggedright\arraybackslash}p{(\columnwidth - 0\tabcolsep) * \real{1.0000}}@{}}
\toprule()
\begin{minipage}[b]{\linewidth}\raggedright
\begin{quote}
Suivi et contrˆole de l'avancement des projets
\end{quote}
\end{minipage} \\
\midrule()
\endhead
\bottomrule()
\end{longtable}

\begin{quote}
\textbf{Interface chatbot}
\end{quote}

Cette interface pr´esente un chatbot int´egr´e ` a la plateforme
ProTracker, con¸cu pour aider

l'administrateur ` a faciliter le suivi complet de tous les m´ecanismes
des projets, des membres

\begin{quote}
, comme illustre la figure sivante 5.14
\end{quote}

\includegraphics[width=3.30556in,height=4.41528in]{vertopal_f50a6211e6524672a7ad79ef0154e85e/media/image63.png}

Figure 5.14 \emph{-- Interface du chatbot}

\begin{quote}
\textbf{5.6} \textbf{Test et validation}

\textbf{5.6.1} \textbf{Test unitaire}
\end{quote}

Un test unitaire est un type de test logiciel qui vise ` a valider le
bon fonctionnement de chaque

petite unit´e de code (fonction, m´ethode, classe) d'une application, en
isolation du reste du

\begin{quote}
code. minimiser et corriger
\end{quote}

82

\begin{longtable}[]{@{}
  >{\raggedright\arraybackslash}p{(\columnwidth - 2\tabcolsep) * \real{0.5000}}
  >{\raggedright\arraybackslash}p{(\columnwidth - 2\tabcolsep) * \real{0.5000}}@{}}
\toprule()
\multicolumn{2}{@{}>{\raggedright\arraybackslash}p{(\columnwidth - 2\tabcolsep) * \real{1.0000} + 2\tabcolsep}@{}}{%
\begin{minipage}[b]{\linewidth}\raggedright
\begin{quote}
Suivi et contrˆole de l'avancement des projets
\end{quote}
\end{minipage}} \\
\midrule()
\endhead
\begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
\textbf{5.6.2}
\end{quote}
\end{minipage} & \begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
\textbf{Tests unitaires avec Jest}
\end{quote}
\end{minipage} \\
\multicolumn{2}{@{}>{\raggedright\arraybackslash}p{(\columnwidth - 2\tabcolsep) * \real{1.0000} + 2\tabcolsep}@{}}{%
\begin{minipage}[t]{\linewidth}\raggedright
La figure ci-dessous illustre les r´esultats des tests r´ealis´es sur le
composant Attendance

\begin{quote}
(Feuille de pr´esence) avec le framework Jest.
\end{quote}
\end{minipage}} \\
\bottomrule()
\end{longtable}

\begin{longtable}[]{@{}
  >{\raggedright\arraybackslash}p{(\columnwidth - 0\tabcolsep) * \real{1.0000}}@{}}
\toprule()
\begin{minipage}[b]{\linewidth}\raggedright
\includegraphics[width=6.61111in,height=2.44028in]{vertopal_f50a6211e6524672a7ad79ef0154e85e/media/image64.png}
\end{minipage} \\
\midrule()
\endhead
\bottomrule()
\end{longtable}

\begin{quote}
Figure 5.15 \emph{-- Tests du composant Attendance avec Jest}

Les \textbf{5 tests} couvrent les principaux comportements du composant,
notamment :

--- Le rendu sans plantage de l'interface.
\end{quote}

\begin{longtable}[]{@{}
  >{\raggedright\arraybackslash}p{(\columnwidth - 2\tabcolsep) * \real{0.5000}}
  >{\raggedright\arraybackslash}p{(\columnwidth - 2\tabcolsep) * \real{0.5000}}@{}}
\toprule()
\multicolumn{2}{@{}>{\raggedright\arraybackslash}p{(\columnwidth - 2\tabcolsep) * \real{1.0000} + 2\tabcolsep}@{}}{%
\begin{minipage}[b]{\linewidth}\raggedright
\begin{quote}
--- Le chargement des donn´ees de pr´esence au montage du composant.

--- L'ouverture de la fenˆetre modale des justifications.

--- Le calcul du score correct bas´e sur le statut de pr´esence.

--- Le calcul pr´ecis des heures travaill´ees.
\end{quote}

Tous les tests ont ´et´e ex´ecut´es avec succ` es en environ
\textbf{1.68 secondes}, confirmant la stabilit´

\begin{quote}
et la fiabilit´e du composant.
\end{quote}
\end{minipage}} \\
\midrule()
\endhead
\begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
\textbf{5.7}
\end{quote}
\end{minipage} & \begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
\textbf{Conslusion}
\end{quote}
\end{minipage} \\
\multicolumn{2}{@{}>{\raggedright\arraybackslash}p{(\columnwidth - 2\tabcolsep) * \real{1.0000} + 2\tabcolsep}@{}}{%
\begin{minipage}[t]{\linewidth}\raggedright
Le sprint 3 de Pro-tracker se concentre sur la visualisation des
donn´ees, le suivi des projets et

la gestion des ´equipes, avec des fonctionnalit´es comme la d´efinition
des jalons, un tableau de

bord KPIs, la gestion financi` ere, le suivi de pr´esence, et la gestion
des r´ealisations et blˆames.

Les notifications am´eliorent la coordination et la communication,
tandis qu'un chatbot facilite

\begin{quote}
l'acc` es aux informations internes.
\end{quote}
\end{minipage}} \\
\bottomrule()
\end{longtable}

83

\begin{longtable}[]{@{}
  >{\raggedright\arraybackslash}p{(\columnwidth - 2\tabcolsep) * \real{0.5000}}
  >{\raggedright\arraybackslash}p{(\columnwidth - 2\tabcolsep) * \real{0.5000}}@{}}
\toprule()
\begin{minipage}[b]{\linewidth}\raggedright
Chapitre\textbf{6}
\end{minipage} & \begin{minipage}[b]{\linewidth}\raggedright
\begin{quote}
\textbf{Yejoura : Fonctionnalit´es avanc´ees de gestion de}
\end{quote}
\end{minipage} \\
\midrule()
\endhead
& \begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
\textbf{projet}
\end{quote}
\end{minipage} \\
\bottomrule()
\end{longtable}

84

\begin{longtable}[]{@{}
  >{\raggedright\arraybackslash}p{(\columnwidth - 2\tabcolsep) * \real{0.5000}}
  >{\raggedright\arraybackslash}p{(\columnwidth - 2\tabcolsep) * \real{0.5000}}@{}}
\toprule()
\multicolumn{2}{@{}>{\raggedright\arraybackslash}p{(\columnwidth - 2\tabcolsep) * \real{1.0000} + 2\tabcolsep}@{}}{%
\begin{minipage}[b]{\linewidth}\raggedright
\begin{quote}
Yejoura : Fonctionnalit´es avanc´ees de gestion de projet
\end{quote}
\end{minipage}} \\
\midrule()
\endhead
\begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
\textbf{6.1}
\end{quote}
\end{minipage} & \begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
\textbf{Introduction}
\end{quote}
\end{minipage} \\
\multicolumn{2}{@{}>{\raggedright\arraybackslash}p{(\columnwidth - 2\tabcolsep) * \real{1.0000} + 2\tabcolsep}@{}}{%
Le quatri` eme sprint de d´eveloppement de la plateforme Yejoura, d'une
dur´ee de trois semaines,

a port´e sur l'int´egration de modules de visualisation avanc´ee pour
les projets . sprint a permis

d'impl´ementer des fonctionnalit´es cl´es telles que : la gestion des
projets , des taches , des jalons} \\
\bottomrule()
\end{longtable}

\begin{quote}
, de feuille de temps .
\end{quote}

Ces ajouts visent ` a am´eliorer le pilotage, la performance et la
r´eactivit´e des utilisateurs au

\begin{quote}
sein de la plateforme.
\end{quote}

\begin{longtable}[]{@{}
  >{\raggedright\arraybackslash}p{(\columnwidth - 2\tabcolsep) * \real{0.5000}}
  >{\raggedright\arraybackslash}p{(\columnwidth - 2\tabcolsep) * \real{0.5000}}@{}}
\toprule()
\begin{minipage}[b]{\linewidth}\raggedright
\begin{quote}
\textbf{6.2}
\end{quote}
\end{minipage} & \begin{minipage}[b]{\linewidth}\raggedright
\begin{quote}
\textbf{Backlog du sprint 4}
\end{quote}
\end{minipage} \\
\midrule()
\endhead
\multicolumn{2}{@{}>{\raggedright\arraybackslash}p{(\columnwidth - 2\tabcolsep) * \real{1.0000} + 2\tabcolsep}@{}}{%
\begin{minipage}[t]{\linewidth}\raggedright
Le backlog du sprint 4, qui s'´etend sur trois semaines, met l'accent
sur la finalisation et l'in-

corporation des fonctionnalit´es essentielles du projet
\textbf{Yejoura}. Ce sprint pr´evoit l'int´egration

des \textbf{KPIs}, qui permettent d'afficher les m´etriques de
performance, et l'instauration du \textbf{dia-}

\textbf{gramme de Gantt} pour le suivi visuel de la progression des
projets. L'´equipe sera en-

gag´ee pour am´eliorer les modules existants, y compris la
\textbf{validation} s´ecuris´ee des utilisateurs,

le syst` eme de \textbf{notifications} pour garantir une communication
efficace, ainsi que la gestion

int´egrale des \textbf{projets}, \textbf{tˆaches}, \textbf{jalons} et
\textbf{feuilles de temps}. L'objectif de ce sprint est de

fournir un ensemble homog` ene et fonctionnel de modules qui favorisent
la gestion collaborative

\begin{quote}
et le suivi de productivit´e au sein de l'application.
\end{quote}

Table 6.1 \emph{-- Backlog du Sprint 4 - Yejoura : Fonctionnalit´es
avanc´ees de gestion de projet}

\begin{longtable}[]{@{}
  >{\raggedright\arraybackslash}p{(\columnwidth - 4\tabcolsep) * \real{0.3333}}
  >{\raggedright\arraybackslash}p{(\columnwidth - 4\tabcolsep) * \real{0.3333}}
  >{\raggedright\arraybackslash}p{(\columnwidth - 4\tabcolsep) * \real{0.3333}}@{}}
\toprule()
\begin{minipage}[b]{\linewidth}\raggedright
\begin{quote}
\textbf{Feature}
\end{quote}
\end{minipage} & \begin{minipage}[b]{\linewidth}\raggedright
\begin{quote}
\textbf{User Story}
\end{quote}
\end{minipage} & \begin{minipage}[b]{\linewidth}\raggedright
\begin{quote}
\textbf{Estimation (jours)}
\end{quote}
\end{minipage} \\
\midrule()
\endhead
\begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
\textbf{Authentification}
\end{quote}
\end{minipage} & \begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
- En tant qu'utilisateur, je veux cr´eer un compte

- En tant qu'utilisateur, je veux me connecter avec un email et un mot
de passe

- En tant qu'utilisateur, lorsque j'ai oubli´e mon mot de passe, je veux
le modifier

- En tant qu'utilisateur, je veux me d´econnecter du syst` eme
\end{quote}
\end{minipage} & 2

2

3

1 \\
\bottomrule()
\end{longtable}
\end{minipage}} \\
\bottomrule()
\end{longtable}

85

\begin{longtable}[]{@{}
  >{\raggedright\arraybackslash}p{(\columnwidth - 0\tabcolsep) * \real{1.0000}}@{}}
\toprule()
\begin{minipage}[b]{\linewidth}\raggedright
\begin{quote}
Yejoura : Fonctionnalit´es avanc´ees de gestion de projet
\end{quote}
\end{minipage} \\
\midrule()
\endhead
\bottomrule()
\end{longtable}

\begin{longtable}[]{@{}
  >{\raggedright\arraybackslash}p{(\columnwidth - 4\tabcolsep) * \real{0.3333}}
  >{\raggedright\arraybackslash}p{(\columnwidth - 4\tabcolsep) * \real{0.3333}}
  >{\raggedright\arraybackslash}p{(\columnwidth - 4\tabcolsep) * \real{0.3333}}@{}}
\toprule()
\begin{minipage}[b]{\linewidth}\raggedright
\begin{quote}
\textbf{Profil}
\end{quote}
\end{minipage} & \begin{minipage}[b]{\linewidth}\raggedright
\begin{quote}
- En tant qu'utilisateur, je veux consulter et modifier mon profil

- En tant qu'utilisateur, je veux changer mon mot de passe

- En tant qu'utilisateur, je veux consulter les activit´es r´ecentes
\end{quote}
\end{minipage} & \begin{minipage}[b]{\linewidth}\raggedright
1

1

1
\end{minipage} \\
\midrule()
\endhead
\textbf{Gestion des projets} &
\begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
- En tant que chef de projet, je veux cr´eer, modifier, supprimer et
archiver des projets.

- En tant qu'utilisateur, je veux consulter la liste des projets
sp´ecifique.
\end{quote}

- En tant que utilisateur,je veux ajouter des tˆaches .

\begin{quote}
- En tant que chef de projet, je veux visualiser l'´ech´eancier des
projets ` a travers un diagramme de Gantt pour mieux comprendre la
planification, les d´ependances et le statut des tˆaches.

- En tant que chef de projet,je veux g´erer Backlog et le tableau.

- En tant que chef de projet,je veux consulter le tableau de bord de
chaque projet.

- En tant que chef de projet,je veux consulter les KPIs de chaque
projet.

- En tant que chef de projet,je veux consulter la calendrier de tous les
projets.
\end{quote}
\end{minipage} & 2

1

2

3

2

3

3

2 \\
\begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
\textbf{Equipe´}
\end{quote}
\end{minipage} & \begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
- En tant que chef de projet, je veux inviter des

utilisateurs ` a un projet par invitation d'email .

- En tant que chef de projet, je veux affecter des

utilisateurs ` a un projet .

- En tant qu'utilisateur, je veux suivre l'´etat

d'avancement des tˆaches assign´ees.
\end{quote}
\end{minipage} & 1

1

1 \\
\begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
\textbf{Gestion des jalons}
\end{quote}
\end{minipage} & \begin{minipage}[t]{\linewidth}\raggedright
- En tant que chef de projet, je veux cr´eer des jalons

\begin{quote}
(milstone).
\end{quote}
\end{minipage} & 1 \\
\bottomrule()
\end{longtable}

86

\begin{longtable}[]{@{}
  >{\raggedright\arraybackslash}p{(\columnwidth - 0\tabcolsep) * \real{1.0000}}@{}}
\toprule()
\begin{minipage}[b]{\linewidth}\raggedright
\begin{quote}
Yejoura : Fonctionnalit´es avanc´ees de gestion de projet
\end{quote}
\end{minipage} \\
\midrule()
\endhead
\bottomrule()
\end{longtable}

\begin{longtable}[]{@{}
  >{\raggedright\arraybackslash}p{(\columnwidth - 4\tabcolsep) * \real{0.3333}}
  >{\raggedright\arraybackslash}p{(\columnwidth - 4\tabcolsep) * \real{0.3333}}
  >{\raggedright\arraybackslash}p{(\columnwidth - 4\tabcolsep) * \real{0.3333}}@{}}
\toprule()
\begin{minipage}[b]{\linewidth}\raggedright
\end{minipage} & \begin{minipage}[b]{\linewidth}\raggedright
\begin{quote}
- En tant que chef de projet, je veux modifier des jalons (milstone).

- En tant que chef de projet, je veux supprimer des jalons (milstone).

- En tant que chef de projet, je veux consulter des jalons (milstone).
\end{quote}
\end{minipage} & \begin{minipage}[b]{\linewidth}\raggedright
1

1

1
\end{minipage} \\
\midrule()
\endhead
\begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
\textbf{Feuilles de temps}
\end{quote}
\end{minipage} & \begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
- En tant qu'utilisateur, je veux enregistrer mes heures de travail
quotidiennement.
\end{quote}
\end{minipage} & 3 \\
\begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
\textbf{Notifications}
\end{quote}
\end{minipage} & \begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
- En tant qu'utilisateur, je veux recevoir des\\
notifications en temps r´eel concernant les mises ` jour des projets,
les tˆaches assign´ees, et les\\
changements de statuts.
\end{quote}\strut
\end{minipage} & 2 \\
\textbf{Internationalisation} &
\begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
- En tant qu'utilisateur, je veux changer la langue de l'application
\end{quote}
\end{minipage} & 3 \\
\bottomrule()
\end{longtable}

\begin{longtable}[]{@{}
  >{\raggedright\arraybackslash}p{(\columnwidth - 2\tabcolsep) * \real{0.5000}}
  >{\raggedright\arraybackslash}p{(\columnwidth - 2\tabcolsep) * \real{0.5000}}@{}}
\toprule()
\begin{minipage}[b]{\linewidth}\raggedright
\begin{quote}
\textbf{6.3}
\end{quote}
\end{minipage} & \begin{minipage}[b]{\linewidth}\raggedright
\begin{quote}
\textbf{Sp´ecification fonctionnelle}
\end{quote}
\end{minipage} \\
\midrule()
\endhead
\multicolumn{2}{@{}>{\raggedright\arraybackslash}p{(\columnwidth - 2\tabcolsep) * \real{1.0000} + 2\tabcolsep}@{}}{%
\begin{minipage}[t]{\linewidth}\raggedright
Dans cette section, nous abordons les fonctionnalit´es du quatri` eme
sprint. Nous commen¸cons

par pr´esenter le diagramme de cas d'utilisation de ce sprint, offrant
une vue d'ensemble des

\begin{quote}
actions possibles des utilisateurs dans notre syst` eme.
\end{quote}
\end{minipage}} \\
\textbf{6.3.1} & \begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
\textbf{Diagramme de cas d'utilisation du quatri` eme sprint}
\end{quote}
\end{minipage} \\
\bottomrule()
\end{longtable}

Le diagramme de cas d'utilisation illustre les actions possibles des
utilisateurs pour le qua-

\begin{longtable}[]{@{}
  >{\raggedright\arraybackslash}p{(\columnwidth - 0\tabcolsep) * \real{1.0000}}@{}}
\toprule()
\begin{minipage}[b]{\linewidth}\raggedright
tri` eme sprint, regroupant toutes les fonctionnalit´es pr´evues. Les
cas d'utilisation qui me sont

\begin{quote}
attribu´es, selon la r´epartition entre d´eveloppeurs, sont en blanc.
\end{quote}
\end{minipage} \\
\midrule()
\endhead
\bottomrule()
\end{longtable}

87

\begin{longtable}[]{@{}
  >{\raggedright\arraybackslash}p{(\columnwidth - 0\tabcolsep) * \real{1.0000}}@{}}
\toprule()
\begin{minipage}[b]{\linewidth}\raggedright
\begin{quote}
Yejoura : Fonctionnalit´es avanc´ees de gestion de projet
\end{quote}
\end{minipage} \\
\midrule()
\endhead
\bottomrule()
\end{longtable}

\includegraphics[width=6.61111in,height=5.36944in]{vertopal_f50a6211e6524672a7ad79ef0154e85e/media/image65.png}

Figure 6.1 \emph{-- diagramme de cas d'utilisation du quatri` eme
sprint}

Il s'agit d'un diagramme de cas d'utilisation pour le sprint 4 d'un
projet, montrant les diff´erents

acteurs et leurs interactions avec le syst` eme. Les relations
''include'' indiquent que certaines

fonctionnalit´es, comme l'authentification, sont n´ecessaires pour
d'autres cas d'utilisation. La

relation ''extend'' entre ''G´erer les tˆaches'' et ''G´erer les
projets'' montre que la gestion des

\begin{quote}
tˆaches fait partie de la gestion des projets.

\textbf{Raffinement de cas d'utilisation}\emph{≪}\textbf{Gestion des
membres}\emph{≫}
\end{quote}

La figure 6.2 illustre le raffinement du cas
d'utilisation\emph{≪}Gestion des membres\emph{≫}afin d'ex-

\begin{quote}
pliquer son aspect fonctionnel.
\end{quote}

88

\begin{longtable}[]{@{}
  >{\raggedright\arraybackslash}p{(\columnwidth - 0\tabcolsep) * \real{1.0000}}@{}}
\toprule()
\begin{minipage}[b]{\linewidth}\raggedright
\begin{quote}
Yejoura : Fonctionnalit´es avanc´ees de gestion de projet
\end{quote}
\end{minipage} \\
\midrule()
\endhead
\bottomrule()
\end{longtable}

\includegraphics[width=6.61111in,height=5.86111in]{vertopal_f50a6211e6524672a7ad79ef0154e85e/media/image66.png}

Figure 6.2 \emph{-- Diagramme de cas d'utilisation raffin´e :≪Gestion
des membres≫}

\begin{quote}
\textbf{6.3.2} \textbf{Description textuelle}

\textbf{Description textuelle de cas d'utilisation authentification}
\end{quote}

\begin{longtable}[]{@{}
  >{\raggedright\arraybackslash}p{(\columnwidth - 2\tabcolsep) * \real{0.5000}}
  >{\raggedright\arraybackslash}p{(\columnwidth - 2\tabcolsep) * \real{0.5000}}@{}}
\toprule()
\begin{minipage}[b]{\linewidth}\raggedright
\begin{quote}
\textbf{El´´ement}
\end{quote}
\end{minipage} & \begin{minipage}[b]{\linewidth}\raggedright
\begin{quote}
\textbf{Description}
\end{quote}
\end{minipage} \\
\midrule()
\endhead
\begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
Titre
\end{quote}
\end{minipage} & \begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
Authentification
\end{quote}
\end{minipage} \\
\begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
Acteur
\end{quote}
\end{minipage} & \begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
Utilisateur
\end{quote}
\end{minipage} \\
\begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
Pr´e condition
\end{quote}
\end{minipage} & \begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
L'utilisateur doit avoir un compte.
\end{quote}
\end{minipage} \\
\bottomrule()
\end{longtable}

89

\begin{longtable}[]{@{}
  >{\raggedright\arraybackslash}p{(\columnwidth - 0\tabcolsep) * \real{1.0000}}@{}}
\toprule()
\begin{minipage}[b]{\linewidth}\raggedright
\begin{quote}
Yejoura : Fonctionnalit´es avanc´ees de gestion de projet
\end{quote}
\end{minipage} \\
\midrule()
\endhead
\bottomrule()
\end{longtable}

\begin{longtable}[]{@{}
  >{\raggedright\arraybackslash}p{(\columnwidth - 2\tabcolsep) * \real{0.5000}}
  >{\raggedright\arraybackslash}p{(\columnwidth - 2\tabcolsep) * \real{0.5000}}@{}}
\toprule()
\begin{minipage}[b]{\linewidth}\raggedright
\begin{quote}
Post condition
\end{quote}
\end{minipage} & \begin{minipage}[b]{\linewidth}\raggedright
L'utilisateur s'authentifie et profite des fonctionnalit´es

\begin{quote}
de l'application.
\end{quote}
\end{minipage} \\
\midrule()
\endhead
\begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
Sc´enario nominal
\end{quote}
\end{minipage} & \begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
1. L'utilisateur lance l'application.

2. Le syst` eme affiche une interface permettant ` a l'uti-

lisateur de saisir son login et son mot de passe.

3. L'utilisateur saisit son login, son mot de passe dans

les champs correspondants et clique sur le bouton

\emph{≪}Connecter \emph{≫}.

4. Le syst` eme v´erifie automatiquement que toutes les

donn´ees obligatoires sont saisies.

5. Lors de la saisie d'un mot de passe ou d'un login

introuvable dans la base de donn´ees, le syst` eme

affiche Exception 1.
\end{quote}
\end{minipage} \\
\begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
Exceptions
\end{quote}
\end{minipage} & --- Exception 1 : Le syst` eme
affiche\emph{≪}incorrecte\emph{≫}. \\
\bottomrule()
\end{longtable}

\begin{longtable}[]{@{}
  >{\raggedright\arraybackslash}p{(\columnwidth - 0\tabcolsep) * \real{1.0000}}@{}}
\toprule()
\begin{minipage}[b]{\linewidth}\raggedright
\begin{quote}
\textbf{Description textuelle de cas d'utilisation -- G´erer membres}
\end{quote}

Table 6.3 \emph{-- Cas d'utilisation -- G´erer membres}

\begin{longtable}[]{@{}
  >{\raggedright\arraybackslash}p{(\columnwidth - 2\tabcolsep) * \real{0.5000}}
  >{\raggedright\arraybackslash}p{(\columnwidth - 2\tabcolsep) * \real{0.5000}}@{}}
\toprule()
\begin{minipage}[b]{\linewidth}\raggedright
\begin{quote}
\textbf{El´´ement}
\end{quote}
\end{minipage} & \begin{minipage}[b]{\linewidth}\raggedright
\begin{quote}
\textbf{Description}
\end{quote}
\end{minipage} \\
\midrule()
\endhead
\begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
Titre
\end{quote}
\end{minipage} & \begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
Invitation d'un membre ` a rejoindre un projet
\end{quote}
\end{minipage} \\
\begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
Acteur
\end{quote}
\end{minipage} & \begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
chef de projet
\end{quote}
\end{minipage} \\
\begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
Pr´e conditions
\end{quote}
\end{minipage} & \begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
--- Le chef de projet est authentifi´e dans le syst` eme.
\end{quote}
\end{minipage} \\
\bottomrule()
\end{longtable}
\end{minipage} \\
\midrule()
\endhead
\bottomrule()
\end{longtable}

90

\begin{longtable}[]{@{}
  >{\raggedright\arraybackslash}p{(\columnwidth - 0\tabcolsep) * \real{1.0000}}@{}}
\toprule()
\begin{minipage}[b]{\linewidth}\raggedright
\begin{quote}
Yejoura : Fonctionnalit´es avanc´ees de gestion de projet
\end{quote}
\end{minipage} \\
\midrule()
\endhead
\bottomrule()
\end{longtable}

\begin{longtable}[]{@{}
  >{\raggedright\arraybackslash}p{(\columnwidth - 2\tabcolsep) * \real{0.5000}}
  >{\raggedright\arraybackslash}p{(\columnwidth - 2\tabcolsep) * \real{0.5000}}@{}}
\toprule()
\begin{minipage}[b]{\linewidth}\raggedright
\begin{quote}
\textbf{El´´ement}
\end{quote}
\end{minipage} & \begin{minipage}[b]{\linewidth}\raggedright
\begin{quote}
\textbf{Description}
\end{quote}
\end{minipage} \\
\midrule()
\endhead
\begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
Sc´enario nominal
\end{quote}
\end{minipage} & \begin{minipage}[t]{\linewidth}\raggedright
--- Le chef de projet acc` ede ` a la section\emph{≪}Gestion des

\begin{quote}
membres\emph{≫}.
\end{quote}

--- Il s´electionne l'option\emph{≪}Inviter un membre existant\emph{≫}.

--- Le syst` eme affiche une liste ou un champ de recherche

\begin{quote}
des utilisateurs existants.

--- Le chef de projet s´electionne le membre souhait´e.
\end{quote}

--- Le membre est ajout´e au projet cible en tant que

\begin{quote}
inviter.
\end{quote}
\end{minipage} \\
\begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
Post conditions
\end{quote}
\end{minipage} & \begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
--- Le membre est li´e au nouveau projet.

--- Le syst` eme notifie le membre de son ajout.
\end{quote}
\end{minipage} \\
\bottomrule()
\end{longtable}

\begin{longtable}[]{@{}
  >{\raggedright\arraybackslash}p{(\columnwidth - 2\tabcolsep) * \real{0.5000}}
  >{\raggedright\arraybackslash}p{(\columnwidth - 2\tabcolsep) * \real{0.5000}}@{}}
\toprule()
\begin{minipage}[b]{\linewidth}\raggedright
\begin{quote}
\textbf{6.4}
\end{quote}
\end{minipage} & \begin{minipage}[b]{\linewidth}\raggedright
\begin{quote}
\textbf{Conception}
\end{quote}
\end{minipage} \\
\midrule()
\endhead
\begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
\textbf{6.4.1}
\end{quote}
\end{minipage} & \begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
\textbf{Diagrammes de s´equences}
\end{quote}
\end{minipage} \\
\multicolumn{2}{@{}>{\raggedright\arraybackslash}p{(\columnwidth - 2\tabcolsep) * \real{1.0000} + 2\tabcolsep}@{}}{%
\begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
\textbf{Diagramme de s´equence de l'authentification}

Ce diagramme de s´equence pr´esente l'authentification de la plateforme
yejoura .
\end{quote}
\end{minipage}} \\
\bottomrule()
\end{longtable}

91

\begin{longtable}[]{@{}
  >{\raggedright\arraybackslash}p{(\columnwidth - 0\tabcolsep) * \real{1.0000}}@{}}
\toprule()
\begin{minipage}[b]{\linewidth}\raggedright
\begin{quote}
Yejoura : Fonctionnalit´es avanc´ees de gestion de projet
\end{quote}
\end{minipage} \\
\midrule()
\endhead
\bottomrule()
\end{longtable}

\includegraphics[width=6.61111in,height=6.94444in]{vertopal_f50a6211e6524672a7ad79ef0154e85e/media/image67.png}

\begin{longtable}[]{@{}
  >{\raggedright\arraybackslash}p{(\columnwidth - 0\tabcolsep) * \real{1.0000}}@{}}
\toprule()
\begin{minipage}[b]{\linewidth}\raggedright
Figure 6.3 \emph{-- diagramme de cas d'utilisation du quatri` eme
sprint}
\end{minipage} \\
\midrule()
\endhead
\bottomrule()
\end{longtable}

92

\begin{longtable}[]{@{}
  >{\raggedright\arraybackslash}p{(\columnwidth - 0\tabcolsep) * \real{1.0000}}@{}}
\toprule()
\begin{minipage}[b]{\linewidth}\raggedright
\begin{quote}
Yejoura : Fonctionnalit´es avanc´ees de gestion de projet
\end{quote}
\end{minipage} \\
\midrule()
\endhead
\begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
\textbf{Diagramme de s´equence}\emph{≪}\textbf{Inviter un
utilisateur}\emph{≫}
\end{quote}
\end{minipage} \\
\bottomrule()
\end{longtable}

\includegraphics[width=6.61111in,height=5.75417in]{vertopal_f50a6211e6524672a7ad79ef0154e85e/media/image68.png}

Figure 6.4 \emph{-- Diagramme de s´equence≪Inviter un utilisateur≫}

\begin{quote}
\textbf{6.4.2} \textbf{Diagramme de classes du sprint 4}

La figure 6.5 illustre le digramme de classes de sprint 4.
\end{quote}

93

\begin{longtable}[]{@{}
  >{\raggedright\arraybackslash}p{(\columnwidth - 0\tabcolsep) * \real{1.0000}}@{}}
\toprule()
\begin{minipage}[b]{\linewidth}\raggedright
\begin{quote}
Yejoura : Fonctionnalit´es avanc´ees de gestion de projet
\end{quote}
\end{minipage} \\
\midrule()
\endhead
\bottomrule()
\end{longtable}

\includegraphics[width=6.61111in,height=3.89583in]{vertopal_f50a6211e6524672a7ad79ef0154e85e/media/image69.png}

Figure 6.5 \emph{-- Diagramme de classe du sprint 4}

\begin{quote}
\textbf{Description du diagramme de classes}
\end{quote}

Le diagramme ci-dessous repr´esente les entit´es principales du syst`
eme de gestion de projets,

\begin{quote}
ainsi que leurs relations :

\textbf{Description du diagramme de classes}
\end{quote}

Ce diagramme mod´elise un syst` eme de gestion de projets et de suivi
des employ´es. Il repose

\begin{quote}
sur les entit´es suivantes :

--- \textbf{User} : Repr´esente un compte utilisateur avec les attributs
: \emph{id, username, email, pass-}

\emph{word}. Chaque utilisateur poss` ede un seul \textbf{Profile}.

--- \textbf{Profile} : Contient les informations personnelles d'un
utilisateur : \emph{id, FirstName, Last-}
\end{quote}

\begin{longtable}[]{@{}
  >{\raggedright\arraybackslash}p{(\columnwidth - 0\tabcolsep) * \real{1.0000}}@{}}
\toprule()
\begin{minipage}[b]{\linewidth}\raggedright
\begin{quote}
\emph{Name, Avatar, cost, Departement, Joindate, Phone, skills, status}.
Il est li´e ` a plusieurs

entit´es : \textbf{Recent activity}, \textbf{Notification},
\textbf{Operation}, et \textbf{Timesheet}. Chaque profil

peut g´erer plusieurs \textbf{Projects}.
\end{quote}
\end{minipage} \\
\midrule()
\endhead
\bottomrule()
\end{longtable}

94

\begin{longtable}[]{@{}
  >{\raggedright\arraybackslash}p{(\columnwidth - 0\tabcolsep) * \real{1.0000}}@{}}
\toprule()
\begin{minipage}[b]{\linewidth}\raggedright
\begin{quote}
Yejoura : Fonctionnalit´es avanc´ees de gestion de projet
\end{quote}
\end{minipage} \\
\midrule()
\endhead
\begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
--- \textbf{Notification} : Repr´esente une notification envoy´e ` a un
utilisateur. Attributs : \emph{id, name,}
\end{quote}
\end{minipage} \\
\bottomrule()
\end{longtable}

\begin{quote}
\emph{description, date}.

--- \textbf{Project} : Repr´esente un projet g´er´e par un profil.
Attributs : \emph{id, name, description,}

\emph{startdate, enddate, deadline, budget, priority, progress, status}.
Un projet peut contenir

plusieurs \textbf{Tasks} et \textbf{Milestones}.

--- \textbf{Task} : Repr´esente une tˆache du projet. Attributs :
\emph{id, name, description, startdate,}

\emph{enddate, status, type}. Chaque tˆache peut recevoir un
\textbf{Commentaire}.

--- \textbf{Comment} : Contient les remarques li´ees ` a une tˆache.
Attributs : \emph{id, CommentText}.

\textbf{6.5} \textbf{R´ealisation}
\end{quote}

Dans cette partie, nous pr´esentons les interfaces utilisateur de notre
plateforme Yejoura,

d´evelopp´ee au cours du quatri` eme sprint. Les captures d'´ecran
suivantes illustrent visuelle-

ment les principales fonctionnalit´es ainsi que les interactions
possibles avec l'application. Il est

\begin{quote}
a noter que seules les interfaces que j'ai personnellement r´ealis´ees
sont pr´esent´ees ici.

\textbf{6.5.1} \textbf{Authentification}
\end{quote}

Permet ` a un utilisateur de s'identifier dans le syst` eme ` a l'aide
de son nom d'utilisateur et de

\begin{quote}
son mot de passe.

\textbf{Interface register et login}
\end{quote}

\begin{longtable}[]{@{}
  >{\raggedright\arraybackslash}p{(\columnwidth - 0\tabcolsep) * \real{1.0000}}@{}}
\toprule()
\begin{minipage}[b]{\linewidth}\raggedright
Lorsqu'un utilisateur souhaite acc´eder ` a la plateforme, il doit tout
d'abord cr´eer un compte

via l'interface d'enregistrement. Ensuite, il peut se connecter grˆace `
a l'interface de login. La

\begin{quote}
figure 6.6 pr´esente ces deux interfaces cˆote ` a cˆote.
\end{quote}
\end{minipage} \\
\midrule()
\endhead
\bottomrule()
\end{longtable}

95

\begin{longtable}[]{@{}
  >{\raggedright\arraybackslash}p{(\columnwidth - 0\tabcolsep) * \real{1.0000}}@{}}
\toprule()
\begin{minipage}[b]{\linewidth}\raggedright
\begin{quote}
Yejoura : Fonctionnalit´es avanc´ees de gestion de projet
\end{quote}
\end{minipage} \\
\midrule()
\endhead
\bottomrule()
\end{longtable}

\begin{quote}
\includegraphics[width=2.975in,height=3.59167in]{vertopal_f50a6211e6524672a7ad79ef0154e85e/media/image70.png}

\emph{(a) Interface registre}

\includegraphics[width=2.975in,height=3.55278in]{vertopal_f50a6211e6524672a7ad79ef0154e85e/media/image71.png}
\end{quote}

\emph{(b) Interface login}

Figure 6.6 \emph{-- Interfaces d'enregistrement et de connexion de la
plateforme yejoura}

\begin{quote}
\textbf{Interface de r´einitialisation du mot de passe}
\end{quote}

Lorsqu'un utilisateur oublie son mot de passe, il peut en demander la
r´einitialisation, puis en

d´efinir un nouveau. Les figures 6.7 et 6.8 montrent respectivement les
interfaces de demande

\begin{quote}
et de modification du mot de passe.

\includegraphics[width=3.17361in,height=2.04444in]{vertopal_f50a6211e6524672a7ad79ef0154e85e/media/image72.png}

Figure 6.7 \emph{-- Interface Mot de passe oubli´}

\includegraphics[width=3.17361in,height=2.73333in]{vertopal_f50a6211e6524672a7ad79ef0154e85e/media/image73.png}

Figure 6.8 \emph{-- Interface Modifier mot de passe}
\end{quote}

96

\begin{longtable}[]{@{}
  >{\raggedright\arraybackslash}p{(\columnwidth - 2\tabcolsep) * \real{0.5000}}
  >{\raggedright\arraybackslash}p{(\columnwidth - 2\tabcolsep) * \real{0.5000}}@{}}
\toprule()
\multicolumn{2}{@{}>{\raggedright\arraybackslash}p{(\columnwidth - 2\tabcolsep) * \real{1.0000} + 2\tabcolsep}@{}}{%
\begin{minipage}[b]{\linewidth}\raggedright
\begin{quote}
Yejoura : Fonctionnalit´es avanc´ees de gestion de projet
\end{quote}
\end{minipage}} \\
\midrule()
\endhead
\begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
\textbf{6.5.2}
\end{quote}
\end{minipage} & \begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
\textbf{Gestion des projets et des membres}
\end{quote}
\end{minipage} \\
\bottomrule()
\end{longtable}

\begin{quote}
\textbf{Interface Liste des membres}
\end{quote}

Cette interface repr´esente la liste des membres sp´ecifique piur chaque
projet, comme montre

\begin{quote}
La figure suivante 6.9.
\end{quote}

\includegraphics[width=6.61111in,height=2.92361in]{vertopal_f50a6211e6524672a7ad79ef0154e85e/media/image74.png}

Figure 6.9 \emph{-- Interface de liste des membres}

\begin{quote}
\textbf{Interfaces d'ajout de membres}
\end{quote}

Les interfaces d'ajout de membres permettent ` a l'utilisateur soit
d'inviter un nouveau membre

\begin{quote}
(figure 6.10), soit d'ajouter un membre existant ` a un projet donn´e
(figure 6.11).

\includegraphics[width=2.975in,height=2.31389in]{vertopal_f50a6211e6524672a7ad79ef0154e85e/media/image75.png}

\includegraphics[width=2.975in,height=2.28611in]{vertopal_f50a6211e6524672a7ad79ef0154e85e/media/image76.png}

Figure 6.11 \emph{-- Interface ajouter un membre}
\end{quote}

\begin{longtable}[]{@{}
  >{\raggedright\arraybackslash}p{(\columnwidth - 2\tabcolsep) * \real{0.5000}}
  >{\raggedright\arraybackslash}p{(\columnwidth - 2\tabcolsep) * \real{0.5000}}@{}}
\toprule()
\begin{minipage}[b]{\linewidth}\raggedright
\begin{quote}
Figure 6.10 \emph{-- Interface inviter un membre}
\end{quote}
\end{minipage} & \begin{minipage}[b]{\linewidth}\raggedright
\begin{quote}
\emph{existant}
\end{quote}
\end{minipage} \\
\midrule()
\endhead
\bottomrule()
\end{longtable}

97

\begin{longtable}[]{@{}
  >{\raggedright\arraybackslash}p{(\columnwidth - 0\tabcolsep) * \real{1.0000}}@{}}
\toprule()
\begin{minipage}[b]{\linewidth}\raggedright
\begin{quote}
Yejoura : Fonctionnalit´es avanc´ees de gestion de projet
\end{quote}
\end{minipage} \\
\midrule()
\endhead
\bottomrule()
\end{longtable}

\begin{quote}
\textbf{Interface gantt chart}
\end{quote}

L'interface Gantt Chart permet de visualiser la planification du projet
sous forme de dia-

gramme de Gantt, en repr´esentant graphiquement les tˆaches, comme
montre La figure suivante

\begin{quote}
6.12.
\end{quote}

\includegraphics[width=5.95in,height=3.20139in]{vertopal_f50a6211e6524672a7ad79ef0154e85e/media/image77.png}

Figure 6.12 \emph{-- Interface gantt chart}

\begin{quote}
\textbf{Indicateurs de performance (KPIs)}
\end{quote}

\begin{longtable}[]{@{}
  >{\raggedright\arraybackslash}p{(\columnwidth - 6\tabcolsep) * \real{0.2500}}
  >{\raggedright\arraybackslash}p{(\columnwidth - 6\tabcolsep) * \real{0.2500}}
  >{\raggedright\arraybackslash}p{(\columnwidth - 6\tabcolsep) * \real{0.2500}}
  >{\raggedright\arraybackslash}p{(\columnwidth - 6\tabcolsep) * \real{0.2500}}@{}}
\toprule()
\multicolumn{4}{@{}>{\raggedright\arraybackslash}p{(\columnwidth - 6\tabcolsep) * \real{1.0000} + 6\tabcolsep}@{}}{%
\begin{minipage}[b]{\linewidth}\raggedright
Pour chaque projet, un ensemble d'indicateurs cl´es de performance
(KPIs) a ´et´e d´efini afin de

mesurer l'efficacit´e, la qualit´e, et le respect des d´elais. Ces KPIs
permettent un suivi structur´

\begin{quote}
et une ´evaluation objective de l'´etat d'avancement du projet.

\textbf{1. Pr´ecision des estimations}
\end{quote}

La pr´ecision des estimations mesure la fid´elit´e entre le temps
estim´e pour une tˆache et le temps

\begin{quote}
r´eellement pass´e.
\end{quote}
\end{minipage}} \\
\midrule()
\endhead
\begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
\textbf{Pr´ecision :}
\end{quote}
\end{minipage} & \emph{Pr}´\emph{ecision} = & 1
\emph{−Tr}´\emph{eel−Testim}´

\emph{Testim}´ & \begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
\emph{×} 100
\end{quote}
\end{minipage} \\
\multicolumn{4}{@{}>{\raggedright\arraybackslash}p{(\columnwidth - 6\tabcolsep) * \real{1.0000} + 6\tabcolsep}@{}}{%
\begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
\textbf{Seuils d'interpr´etation :}
\end{quote}
\end{minipage}} \\
\bottomrule()
\end{longtable}

98

\begin{longtable}[]{@{}
  >{\raggedright\arraybackslash}p{(\columnwidth - 0\tabcolsep) * \real{1.0000}}@{}}
\toprule()
\begin{minipage}[b]{\linewidth}\raggedright
\begin{quote}
Yejoura : Fonctionnalit´es avanc´ees de gestion de projet
\end{quote}
\end{minipage} \\
\midrule()
\endhead
\begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
--- Bonne pr´ecision : \emph{≥}90\%

--- Acceptable : entre 75\% et 89\%

--- ` A am´eliorer : \emph{\textless{}} 75\%
\end{quote}
\end{minipage} \\
\bottomrule()
\end{longtable}

\includegraphics[width=6.61111in,height=2.6125in]{vertopal_f50a6211e6524672a7ad79ef0154e85e/media/image78.png}

Figure 6.13 \emph{-- Interface de mesure de la pr´ecision des
estimations}

\begin{quote}
\textbf{2. Temps de cycle}

Le temps de cycle mesure la dur´ee ´ecoul´ee entre le d´ebut et la fin
d'une tˆache.
\end{quote}

\emph{Tempsdecycle} = \emph{Datedefin −Dateded}´\emph{ebut}

\begin{quote}
\textbf{Seuils :}

--- Tˆache standard : \emph{\textless{}} 3 jours ouvr´es

--- Tˆache complexe : \emph{≤}10 jours ouvr´es
\end{quote}

\includegraphics[width=5.95in,height=1.74861in]{vertopal_f50a6211e6524672a7ad79ef0154e85e/media/image79.png}

Figure 6.14 \emph{-- Interface de mesure du temps de cycle}

99

\begin{longtable}[]{@{}
  >{\raggedright\arraybackslash}p{(\columnwidth - 0\tabcolsep) * \real{1.0000}}@{}}
\toprule()
\begin{minipage}[b]{\linewidth}\raggedright
\begin{quote}
Yejoura : Fonctionnalit´es avanc´ees de gestion de projet
\end{quote}
\end{minipage} \\
\midrule()
\endhead
\bottomrule()
\end{longtable}

\begin{quote}
\textbf{3. Ratio de bugs}
\end{quote}

\begin{longtable}[]{@{}
  >{\raggedright\arraybackslash}p{(\columnwidth - 4\tabcolsep) * \real{0.3333}}
  >{\raggedright\arraybackslash}p{(\columnwidth - 4\tabcolsep) * \real{0.3333}}
  >{\raggedright\arraybackslash}p{(\columnwidth - 4\tabcolsep) * \real{0.3333}}@{}}
\toprule()
\multicolumn{3}{@{}>{\raggedright\arraybackslash}p{(\columnwidth - 4\tabcolsep) * \real{1.0000} + 4\tabcolsep}@{}}{%
\begin{minipage}[b]{\linewidth}\raggedright
Ce ratio exprime la qualit´e des livrables en fonction du nombre de bugs
d´etect´es par rapport

\begin{quote}
au nombre total de tˆaches r´ealis´ees.
\end{quote}
\end{minipage}} \\
\midrule()
\endhead
\emph{Ratiodebugs} = & \begin{minipage}[t]{\linewidth}\raggedright
\emph{Nombre −de −bugs}\\
\emph{Nombre −total −de −t}ˆ\emph{aches}\strut
\end{minipage} & \begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
\emph{×} 100
\end{quote}
\end{minipage} \\
\multicolumn{3}{@{}>{\raggedright\arraybackslash}p{(\columnwidth - 4\tabcolsep) * \real{1.0000} + 4\tabcolsep}@{}}{%
\begin{minipage}[t]{\linewidth}\raggedright
\textbf{Description :} Le ratio de bugs est calcul´e en divisant le
nombre de bugs d´etect´es par le

\begin{quote}
nombre total de tˆaches r´ealis´ees, puis multipli´e par 100 pour
obtenir un pourcentage.
\end{quote}
\end{minipage}} \\
\bottomrule()
\end{longtable}

\begin{quote}
\textbf{Seuils :}\\
--- Excellent : \emph{\textless{}} 5\%\\
--- Acceptable : entre 5\% et 10\%\\
--- Critique : \emph{\textgreater{}} 10\%
\end{quote}

\includegraphics[width=6.61111in,height=2.03611in]{vertopal_f50a6211e6524672a7ad79ef0154e85e/media/image80.png}

Figure 6.15 \emph{-- Interface de suivi du ratio de bugs}

\begin{quote}
\textbf{4. Taux de compl´etion des projets}
\end{quote}

\begin{longtable}[]{@{}
  >{\raggedright\arraybackslash}p{(\columnwidth - 4\tabcolsep) * \real{0.3333}}
  >{\raggedright\arraybackslash}p{(\columnwidth - 4\tabcolsep) * \real{0.3333}}
  >{\raggedright\arraybackslash}p{(\columnwidth - 4\tabcolsep) * \real{0.3333}}@{}}
\toprule()
\multicolumn{3}{@{}>{\raggedright\arraybackslash}p{(\columnwidth - 4\tabcolsep) * \real{1.0000} + 4\tabcolsep}@{}}{%
\begin{minipage}[b]{\linewidth}\raggedright
Cet indicateur mesure l'´etat d'avancement d'un projet en fonction du
nombre de tˆaches ter-

\begin{quote}
min´ees.
\end{quote}
\end{minipage}} \\
\midrule()
\endhead
\emph{Tauxdecompl}´\emph{etion} = & \emph{T}ˆ\emph{aches
−termin}´\emph{ees}

\emph{T}ˆ\emph{aches −totales} &
\begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
\emph{×} 100
\end{quote}
\end{minipage} \\
\bottomrule()
\end{longtable}

\begin{quote}
\textbf{Seuils :}
\end{quote}

\begin{longtable}[]{@{}
  >{\raggedright\arraybackslash}p{(\columnwidth - 0\tabcolsep) * \real{1.0000}}@{}}
\toprule()
\begin{minipage}[b]{\linewidth}\raggedright
\begin{quote}
--- Projet bien avanc´e : \emph{≥}90\%
\end{quote}
\end{minipage} \\
\midrule()
\endhead
\bottomrule()
\end{longtable}

100

\begin{longtable}[]{@{}
  >{\raggedright\arraybackslash}p{(\columnwidth - 0\tabcolsep) * \real{1.0000}}@{}}
\toprule()
\begin{minipage}[b]{\linewidth}\raggedright
\begin{quote}
Yejoura : Fonctionnalit´es avanc´ees de gestion de projet
\end{quote}
\end{minipage} \\
\midrule()
\endhead
\bottomrule()
\end{longtable}

\begin{quote}
--- En cours : entre 50\% et 89\%

--- Retard : \emph{\textless{}} 50\%
\end{quote}

\includegraphics[width=6.61111in,height=1.34167in]{vertopal_f50a6211e6524672a7ad79ef0154e85e/media/image81.png}

Figure 6.16 \emph{-- Interface du taux de compl´etion des projets}

\begin{quote}
\textbf{5. Taux de livraison ` a temps}
\end{quote}

\begin{longtable}[]{@{}
  >{\raggedright\arraybackslash}p{(\columnwidth - 4\tabcolsep) * \real{0.3333}}
  >{\raggedright\arraybackslash}p{(\columnwidth - 4\tabcolsep) * \real{0.3333}}
  >{\raggedright\arraybackslash}p{(\columnwidth - 4\tabcolsep) * \real{0.3333}}@{}}
\toprule()
\multicolumn{3}{@{}>{\raggedright\arraybackslash}p{(\columnwidth - 4\tabcolsep) * \real{1.0000} + 4\tabcolsep}@{}}{%
\begin{minipage}[b]{\linewidth}\raggedright
\begin{quote}
Ce KPI ´evalue la capacit´e ` a livrer les ´el´ements d'un projet dans
les d´elais impartis.
\end{quote}
\end{minipage}} \\
\midrule()
\endhead
\begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
\emph{Taux −de −livraison −}` \emph{a −temps} =
\end{quote}
\end{minipage} & \emph{Livraisons −}` \emph{a −temps}

\emph{Livraisons −totales} & \begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
\emph{×} 100
\end{quote}
\end{minipage} \\
\multicolumn{3}{@{}>{\raggedright\arraybackslash}p{(\columnwidth - 4\tabcolsep) * \real{1.0000} + 4\tabcolsep}@{}}{%
\begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
\textbf{Seuil recommand´e :}
\end{quote}
\end{minipage}} \\
\bottomrule()
\end{longtable}

\begin{quote}
--- Par jalon (milestone) : \emph{≥}95\%
\end{quote}

\includegraphics[width=6.61111in,height=2.93056in]{vertopal_f50a6211e6524672a7ad79ef0154e85e/media/image82.png}

\begin{longtable}[]{@{}
  >{\raggedright\arraybackslash}p{(\columnwidth - 0\tabcolsep) * \real{1.0000}}@{}}
\toprule()
\begin{minipage}[b]{\linewidth}\raggedright
Figure 6.17 \emph{-- Interface de suivi du taux de livraison ` a temps}
\end{minipage} \\
\midrule()
\endhead
\bottomrule()
\end{longtable}

101

\begin{longtable}[]{@{}
  >{\raggedright\arraybackslash}p{(\columnwidth - 0\tabcolsep) * \real{1.0000}}@{}}
\toprule()
\begin{minipage}[b]{\linewidth}\raggedright
\begin{quote}
Yejoura : Fonctionnalit´es avanc´ees de gestion de projet
\end{quote}
\end{minipage} \\
\midrule()
\endhead
\begin{minipage}[t]{\linewidth}\raggedright
\begin{quote}
\textbf{6. D´etails des tˆaches}

Cette interface d´ecrit les tˆaches et les bugs termin´es avec tous les
d´etails.
\end{quote}
\end{minipage} \\
\bottomrule()
\end{longtable}

\begin{quote}
\includegraphics[width=6.61111in,height=1.55417in]{vertopal_f50a6211e6524672a7ad79ef0154e85e/media/image83.png}
\end{quote}

Figure 6.18 \emph{-- Interface de d´etails des tˆaches}

\begin{quote}
\textbf{6.6} \textbf{Test et validation}

\textbf{6.6.1} \textbf{Test unitaire}

Le test unitaire v´erifie chaque fonction ou m´ethode de fa¸con isol´ee
pour s'assurer qu'elle

fonctionne correctement, facilitant ainsi la d´etection d'erreurs et la
maintenance du code.

\textbf{6.6.2} \textbf{Tests unitaires avec Jest}

La figure ci-dessous illustre les r´esultats des tests r´ealis´es sur le
composant KPIs avec le

framework Jest.
\end{quote}

\begin{longtable}[]{@{}
  >{\raggedright\arraybackslash}p{(\columnwidth - 0\tabcolsep) * \real{1.0000}}@{}}
\toprule()
\begin{minipage}[b]{\linewidth}\raggedright
\includegraphics[width=6.61111in,height=2.48056in]{vertopal_f50a6211e6524672a7ad79ef0154e85e/media/image84.png}
\end{minipage} \\
\midrule()
\endhead
\bottomrule()
\end{longtable}

\begin{quote}
Figure 6.19 \emph{-- Tests du composant KPIs avec Jest}
\end{quote}

102

\begin{longtable}[]{@{}
  >{\raggedright\arraybackslash}p{(\columnwidth - 0\tabcolsep) * \real{1.0000}}@{}}
\toprule()
\begin{minipage}[b]{\linewidth}\raggedright
\begin{quote}
Yejoura : Fonctionnalit´es avanc´ees de gestion de projet
\end{quote}
\end{minipage} \\
\midrule()
\endhead
\bottomrule()
\end{longtable}

\begin{quote}
Les \textbf{5 tests} couvrent les principaux comportements du composant,
notamment :

--- L'affichage de l'´etat de chargement.

--- Le rendu des KPIs une fois les donn´ees charg´ees.

--- La gestion des erreurs.

--- L'affichage des statistiques de bugs et de compl´etion.
\end{quote}

Tous les tests ont ´et´e ex´ecut´es avec succ` es en environ \textbf{5.9
secondes}, confirmant la stabilit´

\begin{quote}
du composant.
\end{quote}

\begin{longtable}[]{@{}
  >{\raggedright\arraybackslash}p{(\columnwidth - 2\tabcolsep) * \real{0.5000}}
  >{\raggedright\arraybackslash}p{(\columnwidth - 2\tabcolsep) * \real{0.5000}}@{}}
\toprule()
\begin{minipage}[b]{\linewidth}\raggedright
\begin{quote}
\textbf{6.7}
\end{quote}
\end{minipage} & \begin{minipage}[b]{\linewidth}\raggedright
\begin{quote}
\textbf{Conclusion}
\end{quote}
\end{minipage} \\
\midrule()
\endhead
\multicolumn{2}{@{}>{\raggedright\arraybackslash}p{(\columnwidth - 2\tabcolsep) * \real{1.0000} + 2\tabcolsep}@{}}{%
\begin{minipage}[t]{\linewidth}\raggedright
La plateforme Yejoura offre une solution compl` ete pour la gestion
collaborative de projets.

Elle permet aux ´equipes de planifier, suivre et optimiser leurs
activit´es ` a travers une plate-

forme intuitive et fonctionnelle. Grˆace ` a l'int´egration de la
feuille de temps, les utilisateurs

peuvent enregistrer les heures travaill´ees, facilitant ainsi
l'´evaluation de la charge et de la pro-

ductivit´e. Les jalons permettent de structurer les projets en ´etapes
cl´es, assurant un meilleur

suivi de l'avancement global. Le syst` eme de notifications en temps
r´eel garantit que chaque

membre reste inform´e des mises ` a jour importantes et des ´ech´eances
` a venir. L'ensemble de

ces fonctionnalit´es, combin´e ` a un tableau de bord interactif et au
suivi des indicateurs de per-

formance (KPIs), fait de Yejoura un outil puissant pour renforcer
l'efficacit´e, la transparence

\begin{quote}
et la coordination au sein des ´equipes.
\end{quote}
\end{minipage}} \\
\bottomrule()
\end{longtable}

103

\begin{quote}
Conclusion et perspective

\textbf{Conclusion et perspective}
\end{quote}

En r´eponse aux besoins croissants des entreprises en mati` ere de
pilotage strat´egique et de

gestion op´erationnelle int´egr´ee, notre projet a permis de concevoir
et de d´evelopper deux pla-

teformes web compl´ementaires : \textbf{Pro-Tracker} et
\textbf{Yejoura}. Ces outils apportent une solution

compl` ete pour la planification, le suivi, l'analyse et la
visualisation des projets et de leurs

\begin{quote}
indicateurs de performance.
\end{quote}

\begin{longtable}[]{@{}
  >{\raggedright\arraybackslash}p{(\columnwidth - 0\tabcolsep) * \real{1.0000}}@{}}
\toprule()
\begin{minipage}[b]{\linewidth}\raggedright
\textbf{Pro-Tracker} s'impose comme un levier de gouvernance en
centralisant les donn´ees financi` eres

et en fournissant des tableaux de bord dynamiques, facilitant ainsi une
prise de d´ecision rapide

et ´eclair´ee. De son cˆot´e, \textbf{Yejoura} am´eliore l'organisation
interne des ´equipes grˆace ` a des

\begin{quote}
fonctionnalit´es collaboratives orient´ees gestion des tˆaches et du
temps.
\end{quote}

\textbf{Perspectives} : Afin de renforcer l'impact de ces outils dans un
contexte professionnel ´elargi,

\begin{quote}
plusieurs pistes d'am´elioration sont envisageables :

--- Int´egration de l'intelligence artificielle pour la pr´ediction des
risques projets ou l'optimi-

sation budg´etaire.

--- D´eploiement mobile (applications Android/iOS) pour une
accessibilit´e accrue en mobi-

lit´e.

--- Ajout d'un module de g´en´eration automatique de rapports
personnalis´es.

--- Mise en place d'un syst` eme de notifications intelligentes et de
rappels.
\end{quote}

En somme, ce projet constitue une base solide pour l'´evolution vers une
suite d'outils m´etiers

compl` ete, adaptable aux besoins sp´ecifiques de toute organisation
souhaitant gagner en effi-

\begin{quote}
cacit´e, en transparence et en maˆıtrise de ses projets.
\end{quote}
\end{minipage} \\
\midrule()
\endhead
\bottomrule()
\end{longtable}

104

\textbf{Webographie}

\begin{quote}
{[}1{]} Sparx Systems Pty Ltd, \emph{Enterprise Architect},

, consult´e le 6 juin 2025.

{[}2{]} Microsoft Corporation, \emph{Visual} \emph{Studio} \emph{Code},
,

consult´e le 6 juin 2025.

{[}3{]} Postman, Inc., \emph{Postman API Platform},, consult´e le 6 juin

2025.

{[}4{]} GitHub, Inc., \emph{GitHub - Where the world builds software},,
consult´

le 6 juin 2025.

{[}5{]} PostgreSQL Global Development Group, \emph{PostgreSQL},,

consult´e le 6 juin 2025.
\end{quote}

{[}6{]} Vercel, \emph{Next.js -- The React Framework},, consult´e le 6
juin 2025.

\begin{quote}
{[}7{]} OpenJS Foundation, \emph{Node.js},, consult´e le 6 juin 2025.

{[}8{]} Microsoft Corporation, \emph{TypeScript},, consult´e le 6

juin 2025.

{[}9{]} Ant Financial Services Group, \emph{Ant Design},, consult´e le 6
juin

2025.
\end{quote}

{[}10{]} Tailwind Labs, \emph{Tailwind CSS},, consult´e le 6 juin 2025.

{[}11{]} Python Software Foundation, \emph{Python Programming Language},

\begin{quote}
, consult´e le 6 juin 2025.
\end{quote}

{[}12{]} Pallets Projects, \emph{Flask} \emph{Web Framework}, ,

\begin{quote}
consult´e le 6 juin 2025.
\end{quote}

{[}13{]} Facebook, Inc., \emph{Jest -- Delightful JavaScript Testing},,
consult´e le 6

\begin{quote}
juin 2025.
\end{quote}

105

\end{document}
